import SwiftUI

/// View for displaying, editing, and creating LLM instance details
struct InstanceDetailView: View {
    // TODO: Share functionality
    // MARK: - Properties
    
    @EnvironmentObject private var container: DIContainer
    @StateObject private var viewModel: InstanceDetailViewModel
    @Environment(\.dismiss) private var dismiss
    
    // UI state
    @State private var showModelSelection = false
    @State private var showProviderSelection = false
    @State private var navigateToProviderDetail = false
    @State private var navigateToModelDetail = false
    @State private var navigateToDuplicatedInstance = false
    @State private var showDeleteConfirmation = false
    
    // Parameter editing
    @State private var showParameterEditView = false
    @State private var editingParameterKey: String = ""
    @State private var editingParameterValue: String = ""
    @State private var isCreatingNewParameter = false
    
    // MARK: - Initialization
    
    /// Initialize with a view model
    init(viewModel: InstanceDetailViewModel) {
        _viewModel = StateObject(wrappedValue: viewModel)
    }
    
    // MARK: - Body
    
    var body: some View {
        Form {
            // Header section with logo and name
            Section {
                if viewModel.isEditing {
                    HStack {
                        Text("Logo")
                        Spacer()
                        InstanceLogoView(
                            instance: viewModel.editableInstance,
                            model: viewModel.associatedModel, 
                            provider: viewModel.associatedProvider,
                            size: 48
                        )
                    }
                    
                    HStack {
                        Text("Name")
                        Spacer()
                        TextField("Instance Name", text: $viewModel.editableInstance.name)
                            .font(.headline)
                            .multilineTextAlignment(.trailing)
                            .lineLimit(1)
                    }
                } else {
                    HStack {
                        // Instance logo
                        InstanceLogoView(
                            instance: viewModel.editableInstance,
                            model: viewModel.associatedModel,
                            provider: viewModel.associatedProvider,
                            size: 48
                        )
                        .padding(.trailing, 16)
                        
                        // Instance name
                        Text(viewModel.editableInstance.name)
                            .font(.headline)
                    }
                    .padding(.top, -4)
                    .padding(.bottom, -4)
                    .listRowBackground(Color.clear)
                }
            }
            
            // Action buttons section (horizontal buttons)
            Section {
                HStack(spacing: 8) {
                    // Duplicate button
                    Button(action: {
                        Task {
                            await viewModel.duplicateInstance()
                            if viewModel.duplicatedInstanceId != nil {
                                await MainActor.run {
                                    navigateToDuplicatedInstance = true
                                }
                            }
                        }
                    }) {
                        VStack {
                            Image(systemName: "doc.on.doc")
                                .foregroundColor(.accentColor)
                                .font(.title2)
                                .padding(.bottom, 4)
                            Text("Duplicate")
                                .foregroundColor(.primary)
                                .font(.caption)
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 70)
                        .background(Color(.systemBackground))
                        .cornerRadius(10)
                        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .disabled(viewModel.isLoading)
                    
                    // Favorite button
                    Button(action: {
                        Task {
                            await viewModel.toggleFavorite()
                        }
                    }) {
                        VStack {
                            Image(systemName: viewModel.editableInstance.isFavorited ? "star.fill" : "star")
                                .foregroundColor(viewModel.editableInstance.isFavorited ? .yellow : .accentColor)
                                .font(.title2)
                                .padding(.bottom, 4)
                            Text("Favorite")
                                .foregroundColor(.primary)
                                .font(.caption)
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 70)
                        .background(Color(.systemBackground))
                        .cornerRadius(10)
                        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .disabled(viewModel.isLoading)

                    // Share button
                    Button(action: {
                        // Share functionality will be implemented later
                        print("Share instance tapped")
                    }) {
                        VStack {
                            Image(systemName: "square.and.arrow.up")
                                .foregroundColor(.accentColor)
                                .font(.title2)
                                .padding(.bottom, 4)
                            Text("Share")
                                .foregroundColor(.primary)
                                .font(.caption)
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 70)
                        .background(Color(.systemBackground))
                        .cornerRadius(10)
                        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .disabled(viewModel.isLoading)
                }
                .listRowInsets(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0))
                .listRowBackground(Color.clear)
            }
            
            // Information section with provider and model
            Section {
                // Provider row
                if let provider = viewModel.associatedProvider {
                    HStack {
                        Text("Provider")
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        ProviderLogoView(provider: provider, size: 24)
                            .padding(.trailing, 8)
                        
                        Text(provider.name)
                            .foregroundColor(.secondary)
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)
                            .font(.caption)
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        if viewModel.isEditing {
                            showProviderSelection = true
                        } else {
                            navigateToProviderDetail = true
                        }
                    }
                }
                
                // Model row
                if let model = viewModel.associatedModel {
                    HStack {
                        Text("Model")
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        ModelLogoView(model: model, provider: viewModel.associatedProvider, size: 24)
                            .padding(.trailing, 8)
                        
                        Text(model.name)
                            .foregroundColor(.secondary)
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)
                            .font(.caption)
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        if viewModel.isEditing {
                            showModelSelection = true
                        } else {
                            navigateToModelDetail = true
                        }
                    }
                }
            }
            
            // System Prompt section
            Section("SYSTEM PROMPT") {
                ZStack(alignment: .topLeading) {
                    // TextEditor for input
                    TextEditor(text: Binding(
                        get: { viewModel.editableInstance.systemPrompt ?? "" },
                        set: { newValue in
                            Task { @MainActor in
                                viewModel.editableInstance.systemPrompt = newValue.isEmpty ? nil : newValue
                            }
                        }
                    ))
                    .font(.body)
                    .frame(minHeight: 44, maxHeight: 200)
                    .background(Color(.systemBackground))
                    .accessibilityLabel("System Prompt")
                }
            }
            
            // Parameters section
            Section(header: parametersHeader) {
                if let params = viewModel.editableInstance.defaultParameters, !params.isEmpty {
                    ForEach(Array(params.keys.sorted()), id: \.self) { key in
                        if let value = params[key] {
                            Button(action: {
                                if viewModel.isEditing {
                                    editingParameterKey = key
                                    editingParameterValue = value
                                    isCreatingNewParameter = false
                                    showParameterEditView = true
                                }
                            }) {
                                HStack {
                                    Text(key)
                                        .foregroundColor(.primary)
                                    Spacer()
                                    Text(displayParameterValue(value))
                                        .foregroundColor(.secondary)
                                        .lineLimit(1)
                                }
                            }
                            .disabled(!viewModel.isEditing)
                        }
                    }
                    .onDelete(perform: viewModel.isEditing ? { indexSet in
                        if viewModel.isEditing {
                            // Convert indexSet to keys array for deletion
                            let keys = Array(viewModel.editableInstance.defaultParameters?.keys.sorted() ?? [])
                            var updatedParams = viewModel.editableInstance.defaultParameters ?? [:]
                            
                            for index in indexSet {
                                if index < keys.count {
                                    updatedParams.removeValue(forKey: keys[index])
                                }
                            }
                            
                            viewModel.editableInstance.defaultParameters = updatedParams
                        }
                    } : nil)
                } else {
                    Text("No default parameters set.")
                        .foregroundColor(.secondary)
                        .padding(.vertical, 8)
                }
            }

            // Delete Instance Section (conditionally displayed)
            if viewModel.isEditing && !viewModel.isCreatingNewInstance {
                Section {
                    Button(role: .destructive) {
                        showDeleteConfirmation = true
                    } label: {
                        HStack {
                            Text("Delete Instance")
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                    .contentShape(Rectangle()) // Make the whole row tappable
                    .disabled(viewModel.isLoading)
                }
            }
        }
        .environment(\.editMode, .constant(viewModel.isEditing ? .active : .inactive))
        // Navigation destinations
        .navigationDestination(isPresented: $navigateToDuplicatedInstance) {
            if let duplicatedId = viewModel.duplicatedInstanceId {
                InstanceDetailView(viewModel: container.makeInstanceDetailViewModel(instanceId: duplicatedId, modelId: nil))
            }
        }
        .navigationDestination(isPresented: $navigateToProviderDetail) {
            if let provider = viewModel.associatedProvider {
                ProviderDetailView(viewModel: container.makeProviderDetailViewModel(providerId: provider.id))
            }
        }
        .navigationDestination(isPresented: $navigateToModelDetail) {
            if let model = viewModel.associatedModel {
                ModelDetailView(viewModel: container.makeModelDetailViewModel(modelId: model.id, providerId: nil))
            }
        }
        .navigationDestination(isPresented: $showProviderSelection) {
            createProviderSelectionView()
        }
        .navigationDestination(isPresented: $showModelSelection) {
            createModelSelectionView()
        }
        .navigationDestination(isPresented: $showParameterEditView) {
            ParameterEditView(
                parameterKey: $editingParameterKey,
                parameterValue: $editingParameterValue,
                isCreatingNew: isCreatingNewParameter,
                thinkingCapabilities: viewModel.associatedModel?.thinkingCapabilities,
                searchingCapabilities: viewModel.associatedModel?.searchingCapabilities,
                onSave: { key, value in
                    // Update or add parameter
                    var updatedParams = viewModel.editableInstance.defaultParameters ?? [:]
                    updatedParams[key] = value
                    viewModel.editableInstance.defaultParameters = updatedParams
                    showParameterEditView = false
                }
            )
        }
        .navigationTitle(navigationTitle)
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(viewModel.isEditing)
        .toolbar {
            // Leading items (Cancel for creation mode or edit mode)
            ToolbarItem(placement: .navigationBarLeading) {
                if viewModel.isCreatingNewInstance || viewModel.isEditing {
                    Button("Cancel") {
                        if viewModel.isCreatingNewInstance {
                            // Handle cancel creation
                            dismiss()
                        } else {
                            // Exit edit mode and revert changes
                            withAnimation {
                                viewModel.cancelEditing()
                            }
                        }
                    }
                }
            }
            
            // Trailing items (Edit/Done/Save)
            ToolbarItem(placement: .navigationBarTrailing) {
                if viewModel.isCreatingNewInstance {
                    // Creation mode - Save button
                    Button("Save") {
                        Task {
                            await viewModel.saveChanges()
                            if viewModel.errorMessage == nil {
                                dismiss()
                            }
                        }
                    }
                    .disabled(viewModel.isLoading)
                } else if viewModel.isEditing {
                    // Edit mode - Done button
                    Button("Done") {
                        Task {
                            await viewModel.saveChanges()
                        }
                    }
                    .disabled(viewModel.isLoading)
                } else {
                    // View mode - Edit button
                    Button("Edit") {
                        withAnimation {
                            viewModel.prepareForEditing()
                        }
                    }
                }
            }
        }
        .toolbar(.hidden, for: .tabBar)
        .overlay {
            if viewModel.isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color.black.opacity(0.2))
                    .ignoresSafeArea()
            }
        }
        .alert("Error", isPresented: Binding(
            get: { viewModel.errorMessage != nil },
            set: { if !$0 { viewModel.errorMessage = nil } }
        )) {
            Button("OK") {
                // Dismiss the alert
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
        .alert("Delete Instance", isPresented: $showDeleteConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                Task {
                    await viewModel.deleteInstance()
                    if viewModel.errorMessage == nil {
                        dismiss()
                    }
                }
            }
        } message: {
            Text("Are you sure you want to delete this instance? This action cannot be undone.")
        }
        .animation(.default, value: viewModel.isEditing)
    }
    
    // MARK: - Computed Properties
    
    /// Header view for the parameters section
    private var parametersHeader: some View {
        HStack {
            Text("PARAMETERS")
            
            Spacer()
            
            if viewModel.isEditing {
                Button(action: {
                    editingParameterKey = ""
                    editingParameterValue = ""
                    isCreatingNewParameter = true
                    showParameterEditView = true
                }) {
                    Image(systemName: "plus")
                        .foregroundColor(.accentColor)
                }
            }
        }
    }
    
    /// Dynamic navigation title based on mode
    private var navigationTitle: String {
        if viewModel.isCreatingNewInstance {
            return "Add Instance"
        } else {
            return "Instance Details"
        }
    }
    
    // MARK: - Helper Methods
    
    /// Create provider selection view
    private func createProviderSelectionView() -> some View {
        ItemSelectionView(
            title: "Select Provider",
            items: viewModel.availableProviders,
            selection: Binding<UUID?>(
                get: { self.viewModel.associatedProvider?.id },
                set: { newProviderId in
                    if let newProviderId = newProviderId {
                        Task {
                            await self.viewModel.instanceProviderDidChange(newProviderId: newProviderId)
                        }
                    }
                }
            ),
            itemContentBuilder: { provider in
                HStack {
                    ProviderLogoView(provider: provider, size: 24)
                        .padding(.trailing, 8)
                    
                    Text(provider.name)
                        .foregroundColor(.primary)
                }
            },
            detailViewBuilder: { providerId in
                ProviderDetailView(viewModel: container.makeProviderDetailViewModel(providerId: providerId))
            },
            onSave: { _ in } // Handled by the binding
        )
    }

    /// Create model selection view
    private func createModelSelectionView() -> some View {
        ItemSelectionView(
            title: "Select Model",
            items: viewModel.availableModelsForCurrentProvider,
            selection: Binding<UUID?>(
                get: { viewModel.editableInstance.modelId },
                set: { newModelId in
                    if let newModelId = newModelId {
                        Task {
                            await viewModel.instanceModelDidChange(newModelId: newModelId)
                        }
                    }
                }
            ),
            itemContentBuilder: { model in
                HStack {
                    ModelLogoView(model: model, provider: viewModel.associatedProvider, size: 24)
                        .padding(.trailing, 8)
                    
                    VStack(alignment: .leading) {
                        Text(model.name)
                            .foregroundColor(.primary)
                        Text(model.modelIdentifier)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            },
            detailViewBuilder: { modelId in
                ModelDetailView(viewModel: container.makeModelDetailViewModel(modelId: modelId, providerId: nil))
            },
            onSave: { _ in } // Handled by the binding
        )
    }
    
    /// Format large numbers with commas
    private func formatNumber(_ number: Int64) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        return formatter.string(from: NSNumber(value: number)) ?? "\(number)"
    }
    
    /// Format parameter value for display (truncate long values)
    private func displayParameterValue(_ value: String) -> String {
        if value.count > 30 {
            return String(value.prefix(27)) + "..."
        }
        return value
    }
}

// MARK: - Preview Provider

#if DEBUG
#Preview("GPT-4.1") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        InstanceDetailView(viewModel: container.makeInstanceDetailViewModel(instanceId: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "A1A2A3A4-B5B6-7890-A1A2-A3A4B5B67890")!), modelId: nil))
    }.environmentObject(container)
}

#Preview("Claude 3.7 Sonnet") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        InstanceDetailView(viewModel: container.makeInstanceDetailViewModel(instanceId: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!), modelId: nil))
    }.environmentObject(container)
}

#Preview("Custom Model 1 (instance)") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        InstanceDetailView(viewModel: container.makeInstanceDetailViewModel(instanceId: BuiltinLLMInitializer.deterministicUUID(from: UUID(uuidString: "C1C2C3C4-D5D6-7890-C1C2-C3C4D5D67890")!), modelId: nil))
    }.environmentObject(container)
}

#Preview("Create New Instance") {
    let container = DIContainer(context: PersistenceController.preview.container.viewContext)
    NavigationStack {
        InstanceDetailView(viewModel: container.makeInstanceDetailViewModel(instanceId: nil, modelId: UUID(uuidString: "B1B2B3B4-C5C6-7890-B1B2-B3B4C5C67890")!))
    }.environmentObject(container)
}
#endif
