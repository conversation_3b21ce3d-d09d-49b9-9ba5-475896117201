{"sourceLanguage": "en", "strings": {"": {}, "%lld": {}, "%lld / %lld": {"localizations": {"en": {"stringUnit": {"state": "new", "value": "%1$lld / %2$lld"}}}}, "%lld actions": {}, "%lld characters": {}, "%lld instances": {}, "%lld prompt%@": {"localizations": {"en": {"stringUnit": {"state": "new", "value": "%1$lld prompt%2$@"}}}}, "%lld selected": {}, "%lld/%lld completed": {"localizations": {"en": {"stringUnit": {"state": "new", "value": "%1$lld/%2$lld completed"}}}}, "• %@": {}, "+%lld": {}, "💡 This error is retryable": {}, "0 actions": {}, "Action Categories": {}, "Action Type": {}, "Actions with a single prompt will execute immediately. Actions with multiple prompts will show a selection sheet for users to choose.": {}, "Actions with a single prompt will execute immediately. Actions with multiple prompts will show a selection sheet for you to choose.": {}, "Add API Configuration": {}, "Add custom option": {}, "Add Instance": {}, "Add Models": {}, "Add Parameter": {}, "Add Prompt": {}, "Additional app behavior settings will be available in future updates.": {}, "All Chat Settings": {}, "An unexpected error occurred with code %lld": {}, "An unexpected error occurred with code %lld: %@": {"localizations": {"en": {"stringUnit": {"state": "new", "value": "An unexpected error occurred with code %1$lld: %2$@"}}}}, "API Base URL": {}, "API CONFIGURATION": {}, "API CONFIGURATION OVERRIDES": {}, "API Documentation": {}, "API Endpoint Path": {}, "API error (status: %lld, message: %@)": {"localizations": {"en": {"stringUnit": {"state": "new", "value": "API error (status: %1$lld, message: %2$@)"}}}}, "API error (status: %lld)": {}, "API error for instance %@: %@": {"comment": "API error with instance context", "localizations": {"en": {"stringUnit": {"state": "new", "value": "API error for instance %1$@: %2$@"}}}}, "API Key": {}, "API Style": {}, "APP API CONFIGURATION": {}, "App Behavior": {}, "App-managed Search": {}, "Are you sure you want to delete this action? This action cannot be undone.": {}, "Are you sure you want to delete this instance? This action cannot be undone.": {}, "Are you sure you want to delete this model? This action cannot be undone.": {}, "Are you sure you want to delete this provider? This action cannot be undone.": {}, "Are you sure you want to delete this setting? This action cannot be undone.": {}, "Authentication failed": {}, "Authentication failed: %@": {}, "Auto Generate Title": {}, "Auto-create default instance": {}, "Auto-generate title triggers once after the first message in new chats when auxiliary instance is configured.": {}, "Automatically create a default instance with the same name as the model when saving.": {}, "Auxiliary Instance": {}, "Auxiliary instances are used for title generation and other utility tasks. Select 'None' to disable these features for this session. Or select an instance below to enable these features.": {}, "Available Actions": {}, "AVAILABLE INSTANCES": {}, "AVAILABLE MODELS": {}, "Base URL": {}, "Basic Information": {}, "BUDGET CONFIGURATION": {}, "Built-in actions that cannot be modified.": {}, "Cancel": {}, "Cannot delete system default setting": {"comment": "System setting permission denied error"}, "CAPABILITIES": {}, "Changes to the settings below will affect all chat sessions using this setting.": {}, "Chat Details": {}, "Chat session not found": {"comment": "Chat session not found error"}, "Chat Settings": {}, "Chat settings not found": {"comment": "Chat settings not found error"}, "Chat title": {}, "Chats": {}, "Check input": {}, "Check internet connection": {}, "Check permissions": {}, "Clear": {}, "Clear All": {}, "Configure which actions are available in different parts of the chat interface.": {}, "Contact support": {}, "Context Message Count": {}, "Context Window": {}, "CONTROL TYPE": {}, "Copy All": {}, "Create New Setting": {}, "Default Chat Setting": {}, "Default parameter option if not specified: [\"low\"].": {}, "Default Thinking Expansion": {}, "Delete": {}, "Delete Action": {}, "Delete Instance": {}, "Delete Model": {}, "Delete Provider": {}, "Delete Setting": {}, "Description": {}, "Detail for Group: %@": {}, "Disable Auxiliary Instance": {}, "Documentation URL": {}, "Done": {}, "Duplicate": {}, "Edit": {}, "Edit API Configuration": {}, "Edit Parameter": {}, "Edit Title": {}, "Edit your message...": {}, "Editing message": {}, "Endpoint Path": {}, "Enter a custom title for this chat session.": {}, "Enter API Key": {}, "Error": {}, "Error Loading Chats": {}, "Error Loading Models": {}, "Error: %@": {}, "Failed to compress image": {"comment": "Image compression failed error"}, "Failed to compress image: %@": {"comment": "Image compression failed with reason"}, "Failed to create placeholder message": {"comment": "Placeholder message creation failed error"}, "Failed to decode base64 data": {"comment": "Base64 decoding failed error"}, "Failed to decode base64 data: %@": {"comment": "Base64 decoding failed with reason"}, "Failed to duplicate chat session": {"comment": "Duplicate session creation failed error"}, "Failed to edit message": {"comment": "Message edit failed error"}, "Failed to encode file to base64": {"comment": "Base64 encoding failed error"}, "Failed to encode file to base64: %@": {"comment": "Base64 encoding failed with reason"}, "Failed to order messages correctly": {"comment": "Message ordering failed error"}, "Failed to parse response": {}, "Failed to parse response: %@": {}, "Failed to read file": {"comment": "File read failed error"}, "Failed to read file: %@": {"comment": "File read failed with reason"}, "Failed to regenerate message": {"comment": "Message regeneration failed error"}, "Failed to save messages": {"comment": "<PERSON><PERSON> save failed error"}, "Failed to send message": {"comment": "Message send failed error"}, "Failed to send message: %@": {"comment": "Message send failed with underlying error"}, "Failed to send to %lld instances": {"comment": "Multi-instance partial failure error"}, "Failed to update message tree": {"comment": "Tree update failed error"}, "Favorite": {}, "Favorited": {}, "Favorites": {}, "File Processing Error": {}, "File size %@MB exceeds maximum %@MB": {"comment": "File too large error", "localizations": {"en": {"stringUnit": {"state": "new", "value": "File size %1$@MB exceeds maximum %2$@MB"}}}}, "File size %lld bytes is too small (minimum: %lld bytes)": {"comment": "File too small error", "localizations": {"en": {"stringUnit": {"state": "new", "value": "File size %1$lld bytes is too small (minimum: %2$lld bytes)"}}}}, "First matching config is used. Falls back to provider default.": {}, "For models that control thinking through prompt content (e.g., Anthropic), the app will automatically read this parameter value from your instance settings and include it in the prompt to enable/disable thinking functionality.": {}, "Format '%@' is not supported by %@": {"comment": "Unsupported file format error", "localizations": {"en": {"stringUnit": {"state": "new", "value": "Format '%1$@' is not supported by %2$@"}}}}, "General": {}, "Hub": {}, "Hub Tab": {}, "I": {}, "Icon": {}, "Identifier": {}, "Important: Ensure the model's declared input/output modalities are compatible with the selected API style. For example, 'OpenAI Image Generation' requires text input and image output from the model.": {}, "Input Modalities": {}, "Instance Groups": {}, "Instance Name": {}, "Invalid message structure": {"comment": "Invalid message tree error"}, "Invalid or corrupted file data": {"comment": "Invalid file data error"}, "Invalid or corrupted file data: %@": {"comment": "Invalid file data with reason"}, "Invalid or unrecognized file type: %@": {"comment": "Invalid MIME type error"}, "Invalid request": {}, "Invalid request: %@": {}, "Large File Warning": {}, "LLM API Debug": {}, "Loading actions...": {}, "Loading Chat...": {}, "Loading Chats...": {}, "Loading Models...": {}, "Loading...": {}, "Logo": {}, "Max Output": {}, "Maximum Budget": {}, "Message Actions": {}, "Message not found": {"comment": "Message not found error"}, "Message...": {}, "Model": {}, "Model Description": {}, "Model Identifier": {}, "Model Name": {}, "Model: %@": {}, "Models": {}, "Modify input": {}, "My Actions": {}, "My Chat Settings": {}, "My Custom Actions": {}, "Name": {}, "Network connection error occurred": {}, "Network connection error occurred: %lld": {}, "New chat sessions will use this setting by default.": {}, "No actions available for this category": {}, "No API Key set": {}, "No Chats": {}, "No custom actions yet": {}, "No default maximum budget if not specified. Normally the maximum tokens of the model is used.": {}, "No default parameters set.": {}, "No Favorite Instances": {}, "No Instance Groups": {}, "No instances available": {}, "No instances available for %@.": {}, "No matching instances for '%@'": {}, "No matching instances for '%@' in %@": {"localizations": {"en": {"stringUnit": {"state": "new", "value": "No matching instances for '%1$@' in %2$@"}}}}, "No matching models for '%@'": {}, "No messages yet": {}, "No models available": {}, "No Models Available": {}, "No models available for %@.": {}, "No overrides. Uses Provider default.": {}, "No responses available": {}, "No results for '%@'": {}, "No text content": {}, "None": {}, "Not specified": {}, "OK": {}, "Output Modalities": {}, "Overview": {}, "PARAMETER CONFIGURATION": {}, "PARAMETER DETAILS": {}, "Parameter Name": {}, "Parameter Value": {}, "PARAMETERS": {}, "Please check file permissions and try again": {"comment": "File read failed recovery suggestion"}, "Please check the file type and try again": {"comment": "Invalid MIME type recovery suggestion"}, "Please try again or use a different file": {"comment": "Encoding/decoding failed recovery suggestion"}, "Please try searching for something else.": {}, "Please use a larger file": {"comment": "File too small recovery suggestion"}, "Please use a smaller file or compress the image": {"comment": "File too large recovery suggestion"}, "Please use a supported file format": {"comment": "Unsupported format recovery suggestion"}, "Please use a valid, non-corrupted file": {"comment": "Invalid file data recovery suggestion"}, "Pricing": {}, "Prompts": {}, "Provider": {}, "Provider Name": {}, "Provider: %@": {}, "Quick Templates:": {}, "Rate limit exceeded. Please wait %lld seconds": {}, "Rate limit exceeded. Please wait and try again": {}, "REASONING EFFORT OPTIONS": {}, "reasoning_effort": {}, "Regenerate Response": {}, "Report issue": {}, "Request cancelled": {}, "Request timeout": {}, "Required": {}, "Responses": {}, "Restart conversation": {}, "Restore Last Chat": {}, "Retry": {}, "Retry if needed": {}, "Retry later": {}, "Save": {}, "Saving...": {}, "Search Chats": {}, "Search functionality is managed by the app using its own search APIs and services. No additional configuration is required.": {}, "Search models, instances & providers": {}, "Search providers and models": {}, "Searching": {}, "Select All": {}, "Select Setting": {}, "Select Test Instances": {}, "Select Text": {}, "Selected Actions": {}, "Send Anyway": {}, "Send Test": {}, "Sending...": {}, "Server unavailable": {}, "Sets the number of previous messages to include as context when sending requests to the LLM. More context helps the AI understand the conversation but uses more tokens. The current user message is always included.": {}, "Setting": {}, "Settings": {}, "Share": {}, "Stream connection error": {}, "Stream connection error: %@": {}, "Streaming was interrupted for instance: %@": {"comment": "Streaming interrupted error"}, "Suggested Parameters": {}, "System action permission denied": {"comment": "System action permission denied error"}, "System Actions": {}, "System Prompt": {}, "SYSTEM PROMPT": {}, "Tap a category to manage its actions. You can create, edit, and organize custom actions for different parts of the interface.": {}, "Tap a prompt to edit it in full screen. You can reorder prompts by dragging.": {}, "Tap the '+ ' button to start a new chat": {}, "Test Message": {}, "The selected file(s) might contain a large number of tokens (%lld estimated). Sending large files could exceed model context limits or consume many tokens.": {}, "There are currently no AI models or providers configured in the app.": {}, "thinking": {}, "Thinking": {}, "thinking_prompt": {}, "thinkingBudget": {}, "This action cannot be undone. Any chat sessions using this setting will be switched to the system default. Cannot delete system default settings or your current default setting.": {}, "This feature is not implemented": {}, "This feature is not implemented: %@": {}, "This is the API parameter for parameter-based thinking control. When using instance with this model, the app will send this parameter to the API to enable/disable thinking functionality. Please refer to your provider and model API documentation to set the correct parameter name and values.": {}, "This is the API parameter for reasoning effort control. When using instance with this model, the app will send this parameter to the API to enable/disable thinking functionality. Please refer to your provider and model API documentation to set the correct parameter name and values.": {}, "This is the API parameter for search control. When using instance with this model, the app will send this parameter to the API to enable/disable search functionality. Please refer to your provider and model API documentation to set the correct parameter name and values.": {}, "This is the API parameter for thinking budget control. When using instance with this model, the app will send this parameter to the API to set the maximum tokens for the thinking process. Please refer to your provider and model API documentation to set the correct parameter values.": {}, "Title": {}, "Token Budget": {}, "Token Count": {}, "tools": {}, "Total Custom Actions": {}, "Tried to navigate to a invalid message": {"comment": "Invalid message navigation error"}, "Try searching for something else.": {}, "Try using a different image or reduce the image size manually": {"comment": "Compression failed recovery suggestion"}, "Type": {}, "Type: %@": {}, "UI & Display": {}, "User not found": {"comment": "User not found error"}, "Verify API key": {}, "Wait and retry": {}, "Website": {}, "Website URL": {}, "When enabled, the app will automatically open the last chat you were viewing when you restart the app.": {}, "You haven't created any instance groups yet. Groups allow you to manage multiple AI models together.": {}, "You haven't marked any AI models as favorites yet.": {}}, "version": "1.0"}