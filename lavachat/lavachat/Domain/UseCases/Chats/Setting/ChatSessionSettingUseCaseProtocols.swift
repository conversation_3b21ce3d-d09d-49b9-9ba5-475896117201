import Foundation
import Combine

// MARK: - ChatSessionSetting UseCase Protocols

/// Retrieves a specific chat session setting by ID.
protocol GetChatSessionSettingUseCaseProtocol {
    func execute(settingId: UUID) async throws -> ChatSessionSetting?
    func executeAndResolveActions(settingId: UUID) async throws -> (setting: ChatSessionSetting, resolvedActions: ResolvedMessageActionSettings)?
}

/// Retrieves all chat session settings.
protocol GetAllChatSessionSettingsUseCaseProtocol {
    func execute() async throws -> [ChatSessionSetting]
}

/// Retrieves the system default chat session setting.
protocol GetDefaultChatSessionSettingUseCaseProtocol {
    func execute() async throws -> ChatSessionSetting
}

/// Creates a new chat session setting using direct entity approach.
protocol CreateChatSessionSettingUseCaseProtocol {
    func execute(_ setting: ChatSessionSetting) async throws -> ChatSessionSetting
}

/// Updates an existing chat session setting using direct entity approach.
protocol UpdateChatSessionSettingUseCaseProtocol {
    func execute(_ setting: ChatSessionSetting) async throws -> ChatSessionSetting
}

/// Updates the user's default chat session setting.
protocol UpdateUserDefaultChatSettingUseCaseProtocol {
    func execute(settingId: UUID) async throws -> User
}

/// Deletes a chat session setting.
protocol DeleteChatSessionSettingUseCaseProtocol {
    func execute(settingId: UUID) async throws
}