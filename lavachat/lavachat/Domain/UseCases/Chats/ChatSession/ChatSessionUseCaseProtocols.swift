import Foundation
import Combine

// MARK: - ChatSession UseCase Protocols

/// Creates a new chat session with optional title and LLM instance.
protocol CreateChatSessionUseCaseProtocol {
    func execute(title: String?, llmInstanceId: UUID?, instanceSettings: [UUID: SessionInstanceSetting]?) async throws -> ChatSession
}

/// Retrieves a specific chat session by ID.
protocol GetChatSessionUseCaseProtocol {
    func execute(sessionId: UUID) async throws -> ChatSession?
}

/// Retrieves all chat sessions for the current user.
protocol GetAllChatSessionsUseCaseProtocol {
    func execute() async throws -> [ChatSession]
}

/// Updates an existing chat session using direct entity approach.
protocol UpdateChatSessionUseCaseProtocol {
    func execute(_ session: ChatSession) async throws -> ChatSession
}

/// Deletes a chat session and its associated messages.
protocol DeleteChatSessionUseCaseProtocol {
    func execute(sessionId: UUID) async throws
}

/// Creates a duplicate of an existing chat session.
protocol DuplicateChatSessionUseCaseProtocol {
    func execute(sourceSession: ChatSession) async throws -> ChatSession
}