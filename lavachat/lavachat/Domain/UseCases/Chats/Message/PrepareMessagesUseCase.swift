import Foundation

/// Prepares messages for immediate UI display and subsequent streaming processing
final class PrepareMessagesUseCase: PrepareMessagesUseCaseProtocol {
    
    // MARK: - Dependencies
    
    private let chatRepository: ChatRepository
    private let messageTreeManagerUseCase: MessageTreeManagerUseCaseProtocol
    
    // MARK: - Initialization
    
    init(
        chatRepository: ChatRepository,
        messageTreeManagerUseCase: MessageTreeManagerUseCaseProtocol
    ) {
        self.chatRepository = chatRepository
        self.messageTreeManagerUseCase = messageTreeManagerUseCase
    }
    
    // MARK: - PrepareMessagesUseCaseProtocol
    
    func execute(
        chatSession: ChatSession,
        chatSessionSetting: ChatSessionSetting,
        content: [ContentBlock],
        instanceIds: [UUID],
        operationType: MessageOperationType,
        parentMessageId: UUID?,
        originalMessageId: UUID?,
        userMessageDepth: Int64
    ) async throws -> PreparedMessages {
        
        let userMessage: Message
        let placeholderAssistantMessages: [Message]
        let newAssistantMessageLayer: MessageLayer?
        let overrideSystemPrompt: String?
        
        // Extract control settings from chatSession
        let thinkingControls = extractThinkingControls(from: chatSession, instanceIds: instanceIds, operationType: operationType)
        let searchingControls = extractSearchingControls(from: chatSession, instanceIds: instanceIds, operationType: operationType)

        if operationType.createsUserMessage {
            // Create metadata for user message based on operation type
            let userMetadata = createMetadataForOperation(
                operationType: operationType,
                originalMessageId: originalMessageId,
                role: .user
            )
            
            userMessage = Message(
                sessionId: chatSession.id,
                parentId: parentMessageId,
                role: .user,
                content: content,
                depth: userMessageDepth,
                status: .received,
                metadata: userMetadata
            )
        } else {
            userMessage = Message(
                sessionId: chatSession.id,
                parentId: nil,
                role: .user,
                content: [],
                depth: 0
            ) // Placeholder - will not be used
        }
        
        // Create metadata for assistant message based on operation type
        let assistantMetadata = createMetadataForOperation(
            operationType: operationType,
            originalMessageId: originalMessageId,
            role: .assistant
        )
        
        switch operationType {
        case .newMessage, .editMessage:
            // Create placeholder assistant messages
            let assistantDepth = userMessageDepth + 1
            placeholderAssistantMessages = instanceIds.map { instanceId in
                return Message(
                    sessionId: chatSession.id,
                    parentId: userMessage.id,
                    role: .assistant,
                    content: [.text("")], // Empty placeholder content
                    depth: assistantDepth,
                    llmInstanceId: instanceId,
                    status: .generating,
                    metadata: assistantMetadata
                )
            }
            
            // Create message layer for assistant messages
            newAssistantMessageLayer = messageTreeManagerUseCase.createMessageLayerFromMessages(
                chatSession: chatSession,
                messages: placeholderAssistantMessages
            )
            
        case .regenerateSingleMessage:
            // Create placeholder assistant messages for regeneration
            guard let parentMessageId = parentMessageId else {
                throw ChatError.invalidMessageNavigation
            }

            let assistantDepth = userMessageDepth + 1
            placeholderAssistantMessages = instanceIds.map { instanceId in
                return Message(
                    sessionId: chatSession.id,
                    parentId: parentMessageId,
                    role: .assistant,
                    content: [.text("")], // Empty placeholder content
                    depth: assistantDepth,
                    llmInstanceId: instanceId,
                    status: .generating,
                    metadata: assistantMetadata
                )
            }

            // No new layer for regenerate - existing layer will be updated
            newAssistantMessageLayer = nil

        case .regenerateSingleMessageWithPrompt:
            // Create placeholder assistant messages for regeneration with custom prompt
            guard let parentMessageId = parentMessageId else {
                throw ChatError.invalidMessageNavigation
            }

            let assistantDepth = userMessageDepth + 1
            placeholderAssistantMessages = instanceIds.map { instanceId in
                return Message(
                    sessionId: chatSession.id,
                    parentId: parentMessageId,
                    role: .assistant,
                    content: [.text("")], // Empty placeholder content
                    depth: assistantDepth,
                    llmInstanceId: instanceId,
                    status: .generating,
                    metadata: assistantMetadata
                )
            }

            // No new layer for regenerate - existing layer will be updated
            newAssistantMessageLayer = nil

        case .rewritePromptWithPrompt:
            // Create placeholder assistant messages for prompt rewrite (internal operation)
            let assistantDepth = userMessageDepth + 1
            placeholderAssistantMessages = instanceIds.map { instanceId in
                return Message(
                    sessionId: chatSession.id,
                    parentId: nil, // No parent needed for internal operation
                    role: .assistant,
                    content: [.text("")], // Empty placeholder content
                    depth: assistantDepth,
                    llmInstanceId: instanceId,
                    status: .generating,
                    metadata: assistantMetadata
                )
            }

            // No new layer for internal operation
            newAssistantMessageLayer = nil

        case .generateChatTitle:
            // Create placeholder assistant messages for title generation (internal operation)
            let assistantDepth = userMessageDepth + 1
            placeholderAssistantMessages = instanceIds.map { instanceId in
                return Message(
                    sessionId: chatSession.id,
                    parentId: nil, // No parent needed for internal operation
                    role: .assistant,
                    content: [.text("")], // Empty placeholder content
                    depth: assistantDepth,
                    llmInstanceId: instanceId,
                    status: .generating,
                    metadata: assistantMetadata
                )
            }

            // No new layer for internal operation
            newAssistantMessageLayer = nil
            
        case .regenerateAllMessages:
            // Create placeholder assistant messages for all instances regeneration
            guard let parentMessageId = parentMessageId else {
                throw ChatError.invalidMessageNavigation
            }
            
            let assistantDepth = userMessageDepth + 1
            placeholderAssistantMessages = instanceIds.map { instanceId in
                return Message(
                    sessionId: chatSession.id,
                    parentId: parentMessageId,
                    role: .assistant,
                    content: [.text("")], // Empty placeholder content
                    depth: assistantDepth,
                    llmInstanceId: instanceId,
                    status: .generating,
                    metadata: assistantMetadata
                )
            }
            
            // Create new message layer for all regenerated messages
            newAssistantMessageLayer = messageTreeManagerUseCase.createMessageLayerFromMessages(
                chatSession: chatSession,
                messages: placeholderAssistantMessages
            )
        }
        
        // Create persistence task
        let persistenceTask = Task<Void, Error> {
            // Skip persistence for operations that don't need it
            if operationType.skipsPersistence {
                return
            }

            try await self.persistMessages(
                chatSession: chatSession,
                userMessage: (operationType.createsUserMessage && operationType.persistUserMessage) ? userMessage : nil,
                placeholderMessages: placeholderAssistantMessages,
                operationType: operationType
            )
        }
        
        return PreparedMessages(
            userMessage: userMessage,
            placeholderAssistantMessages: placeholderAssistantMessages,
            newAssistantMessageLayer: newAssistantMessageLayer,
            operationType: operationType,
            overrideSystemPrompt: nil,
            thinkingControls: thinkingControls,
            searchingControls: searchingControls,
            contextMessageCount: chatSessionSetting.contextMessageCount,
            persistenceTask: persistenceTask
        )
    }
    
    // MARK: - Private Methods
    
    /// Create metadata for different operation types
    private func createMetadataForOperation(
        operationType: MessageOperationType,
        originalMessageId: UUID?,
        role: MessageRole
    ) -> [String: String]? {
        
        switch operationType {
        case .editMessage:
            // For edit message operation, set isEditOf metadata for user messages
            if role == .user {
                guard let originalMessageId = originalMessageId else {
                    print("Warning: editMessage operation requires originalMessageId")
                    return nil
                }
                return [Message.MetadataKeys.isEditOf: originalMessageId.uuidString]
            }
            return nil
            
        case .regenerateSingleMessage, .regenerateSingleMessageWithPrompt:
            // For single regenerate operation, set isRegenerationOf metadata for assistant messages
            if role == .assistant {
                guard let originalMessageId = originalMessageId else {
                    print("Warning: \(operationType) operation requires originalMessageId")
                    return nil
                }
                return [Message.MetadataKeys.isRegenerationOf: originalMessageId.uuidString]
            }
            return nil
            
        case .regenerateAllMessages:
            // For regenerate all messages operation, no special metadata needed
            // as we're creating completely new assistant messages for all instances
            return nil

        case .newMessage:
            // New messages don't need special metadata
            return nil

        case .rewritePromptWithPrompt:
            // Internal prompt rewrite operation doesn't need special metadata
            return nil

        case .generateChatTitle:
            // Internal title generation operation doesn't need special metadata
            return nil
        }
    }
    
    /// Extract thinking controls from chat session instance settings
    private func extractThinkingControls(from chatSession: ChatSession, instanceIds: [UUID], operationType: MessageOperationType) -> [UUID: Bool] {
        var thinkingControls: [UUID: Bool] = [:]

        for instanceId in instanceIds {
            // Check if operation type forces thinking to be disabled
            if operationType.forceDisableThinking {
                thinkingControls[instanceId] = false
            } else {
                // Get instance setting, use default if not found
                let instanceSetting = chatSession.instanceSettings?[instanceId]
                thinkingControls[instanceId] = instanceSetting?.thinkingEnabled ?? false
            }
        }

        return thinkingControls
    }
    
    /// Extract searching controls from chat session instance settings
    private func extractSearchingControls(from chatSession: ChatSession, instanceIds: [UUID], operationType: MessageOperationType) -> [UUID: Bool] {
        var searchingControls: [UUID: Bool] = [:]

        for instanceId in instanceIds {
            // Check if operation type forces searching to be disabled
            if operationType.forceDisableSearching {
                searchingControls[instanceId] = false
            } else {
                // Get instance setting, use default if not found
                let instanceSetting = chatSession.instanceSettings?[instanceId]
                searchingControls[instanceId] = instanceSetting?.networkEnabled ?? false
            }
        }

        return searchingControls
    }
    
    private func persistMessages(
        chatSession: ChatSession,
        userMessage: Message?,
        placeholderMessages: [Message],
        operationType: MessageOperationType
    ) async throws {
        // Save user message if exists
        if let userMessage = userMessage {
            _ = try await chatRepository.createMessage(userMessage)
        }
        
        // Save placeholder assistant messages
        for placeholderMessage in placeholderMessages {
            _ = try await chatRepository.createMessage(placeholderMessage)
        }
        
        // Update message tree manager cache
        if let userMessage = userMessage {
            await messageTreeManagerUseCase.addMessageAndUpdateTree(
                sessionId: chatSession.id,
                message: userMessage
            )
        }

        // For some operations, we delay adding placeholder messages to cache
        // This allows SendMessageUseCase to get the correct messageHistory first
        if !operationType.delayAddPlaceholderMessagesToCache {
            await messageTreeManagerUseCase.addMultipleMessagesAndUpdateTree(
                sessionId: chatSession.id,
                messages: placeholderMessages
            )
        }
    }
} 