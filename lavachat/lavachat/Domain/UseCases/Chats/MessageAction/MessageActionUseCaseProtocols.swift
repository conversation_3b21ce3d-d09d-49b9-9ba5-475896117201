import Foundation

// MARK: - MessageAction UseCase Protocols

/// Creates a new message action.
protocol CreateMessageActionUseCaseProtocol {
    func execute(_ action: MessageAction) async throws -> MessageAction
}

/// Retrieves a specific message action by its ID.
protocol GetMessageActionUseCaseProtocol {
    func execute(actionId: UUID) async throws -> MessageAction?
}

/// Retrieves all message actions.
protocol GetAllMessageActionsUseCaseProtocol {
    func execute() async throws -> [MessageAction] // Only user-created actions
    func executeIncludingSystemActions() async throws -> [MessageAction]
}

/// Updates an existing message action.
protocol UpdateMessageActionUseCaseProtocol {
    func execute(_ action: MessageAction) async throws -> MessageAction
}

/// Deletes a message action by its ID.
protocol DeleteMessageActionUseCaseProtocol {
    func execute(actionId: UUID) async throws
}

/// Checks which chat session settings are using a specific message action.
protocol GetMessageActionUsageUseCaseProtocol {
    func execute(actionId: UUID) async throws -> [ChatSessionSetting]
}
