import Foundation
import UIKit

/// Comprehensive file processor for multimodal content
struct FileProcessor {
    
    // MARK: - Processing Results
    
    struct ProcessingResult {
        let contentBlock: ContentBlock
        let originalSize: Int64
        let processedSize: Int64
        let wasCompressed: Bool
        let processingTime: TimeInterval
        
        var compressionRatio: Double {
            guard originalSize > 0 else { return 1.0 }
            return Double(processedSize) / Double(originalSize)
        }
        
        var sizeSavings: Int64 {
            return originalSize - processedSize
        }
    }
    
    // MARK: - Main Processing Methods
    
    /// Processes file data into appropriate ContentBlock for API transmission
    /// - Parameters:
    ///   - data: The file data to process
    ///   - fileName: The name of the file
    ///   - mimeType: The MIME type of the file
    ///   - apiStyle: The target API style
    /// - Returns: Processing result with ContentBlock
    /// - Throws: ChatError wrapping FileProcessingError if processing fails
    static func processFile(
        data: Data,
        fileName: String,
        mimeType: String,
        for apiStyle: APIStyle
    ) throws -> ProcessingResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        let originalSize = Int64(data.count)
        
        do {
            // Validate format
            if let formatError = FileFormatValidator.validateFileFormat(mimeType, for: apiStyle) {
                throw ChatError.fileProcessingFailed(formatError)
            }
            
            let fileType = FileFormatValidator.fileType(from: mimeType)
            var processedData = data
            var wasCompressed = false

            // Check initial size validation
            let sizeValidation = FileSizeValidator.validateSizeWithRecommendations(originalSize, for: apiStyle)

            // For images, try compression before failing on size
            if fileType == .image && sizeValidation.error != nil {
                // Try to compress the image to meet size requirements
                do {
                    processedData = try ImageCompressor.compressImageForAPI(data, apiStyle: apiStyle)
                    wasCompressed = true

                    // Re-validate size after compression
                    let compressedSizeValidation = FileSizeValidator.validateSizeWithRecommendations(Int64(processedData.count), for: apiStyle)
                    if let compressedSizeError = compressedSizeValidation.error {
                        throw ChatError.fileProcessingFailed(compressedSizeError)
                    }
                } catch {
                    // If compression fails, throw the original size error
                    if let sizeError = sizeValidation.error {
                        throw ChatError.fileProcessingFailed(sizeError)
                    }
                    throw error
                }
            } else if fileType == .image && (sizeValidation.needsCompression || sizeValidation.shouldCompress) {
                // Optional compression for better performance
                processedData = try ImageCompressor.compressImageForAPI(data, apiStyle: apiStyle)
                wasCompressed = true
            } else if let sizeError = sizeValidation.error {
                // For non-images, fail immediately if size is too large
                throw ChatError.fileProcessingFailed(sizeError)
            }
            
            // Create appropriate content block
            let contentBlock: ContentBlock
            
            switch fileType {
            case .image:
                let imageInfo = try Base64Encoder.createImageInfo(
                    from: processedData,
                    mimeType: mimeType
                )
                contentBlock = .image(imageInfo)
                
            case .document, .other:
                let fileInfo = try Base64Encoder.createFileInfo(
                    from: processedData,
                    fileName: fileName,
                    mimeType: mimeType
                )
                contentBlock = .file(fileInfo)
            }
            
            let endTime = CFAbsoluteTimeGetCurrent()
            
            return ProcessingResult(
                contentBlock: contentBlock,
                originalSize: originalSize,
                processedSize: Int64(processedData.count),
                wasCompressed: wasCompressed,
                processingTime: endTime - startTime
            )
            
        } catch let chatError as ChatError {
            throw chatError
        } catch {
            throw ChatError.fileProcessingFailed(.invalidFileData(reason: error.localizedDescription))
        }
    }
    
    /// Processes image data specifically
    /// - Parameters:
    ///   - imageData: The image data to process
    ///   - mimeType: The MIME type of the image
    ///   - apiStyle: The target API style
    ///   - caption: Optional caption for the image
    /// - Returns: Processing result with image ContentBlock
    /// - Throws: ChatError if processing fails
    static func processImage(
        data imageData: Data,
        mimeType: String,
        for apiStyle: APIStyle,
        caption: String? = nil
    ) throws -> ProcessingResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        let originalSize = Int64(imageData.count)
        
        do {
            // Validate image format
            guard FileFormatValidator.isValidImageFormat(mimeType, for: apiStyle) else {
                throw ChatError.fileProcessingFailed(.unsupportedFormat(mimeType: mimeType, apiStyle: apiStyle))
            }
            
            // Validate image data
            guard ImageCompressor.isValidImageData(imageData) else {
                throw ChatError.fileProcessingFailed(.invalidFileData(reason: "Invalid image data"))
            }
            
            // Check size and compress if needed
            let sizeValidation = FileSizeValidator.validateSizeWithRecommendations(originalSize, for: apiStyle)

            var processedData = imageData
            var wasCompressed = false

            // Try compression if size validation fails or is recommended
            if sizeValidation.error != nil {
                // Must compress to meet size requirements
                processedData = try ImageCompressor.compressImageForAPI(imageData, apiStyle: apiStyle)
                wasCompressed = true

                // Re-validate size after compression
                let compressedSizeValidation = FileSizeValidator.validateSizeWithRecommendations(Int64(processedData.count), for: apiStyle)
                if let compressedSizeError = compressedSizeValidation.error {
                    throw ChatError.fileProcessingFailed(compressedSizeError)
                }
            } else if sizeValidation.needsCompression || sizeValidation.shouldCompress {
                // Optional compression for better performance
                processedData = try ImageCompressor.compressImageForAPI(imageData, apiStyle: apiStyle)
                wasCompressed = true
            }
            
            // Create ImageInfo with base64 data
            var imageInfo = try Base64Encoder.createImageInfo(
                from: processedData,
                mimeType: mimeType,
                caption: caption
            )
            
            let contentBlock = ContentBlock.image(imageInfo)
            let endTime = CFAbsoluteTimeGetCurrent()
            
            return ProcessingResult(
                contentBlock: contentBlock,
                originalSize: originalSize,
                processedSize: Int64(processedData.count),
                wasCompressed: wasCompressed,
                processingTime: endTime - startTime
            )
            
        } catch let chatError as ChatError {
            throw chatError
        } catch {
            throw ChatError.fileProcessingFailed(.compressionFailed(reason: error.localizedDescription))
        }
    }
    
    /// Processes document data specifically
    /// - Parameters:
    ///   - documentData: The document data to process
    ///   - fileName: The name of the document
    ///   - mimeType: The MIME type of the document
    ///   - apiStyle: The target API style
    /// - Returns: Processing result with file ContentBlock
    /// - Throws: ChatError if processing fails
    static func processDocument(
        data documentData: Data,
        fileName: String,
        mimeType: String,
        for apiStyle: APIStyle
    ) throws -> ProcessingResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        let originalSize = Int64(documentData.count)
        
        do {
            // Validate document format
            guard FileFormatValidator.isValidDocumentFormat(mimeType, for: apiStyle) else {
                throw ChatError.fileProcessingFailed(.unsupportedFormat(mimeType: mimeType, apiStyle: apiStyle))
            }
            
            // Validate size
            if let sizeError = FileSizeValidator.validateFileSize(originalSize, for: apiStyle) {
                throw ChatError.fileProcessingFailed(sizeError)
            }
            
            // Create FileInfo with base64 data
            let fileInfo = try Base64Encoder.createFileInfo(
                from: documentData,
                fileName: fileName,
                mimeType: mimeType
            )
            
            let contentBlock = ContentBlock.file(fileInfo)
            let endTime = CFAbsoluteTimeGetCurrent()
            
            return ProcessingResult(
                contentBlock: contentBlock,
                originalSize: originalSize,
                processedSize: originalSize, // Documents are not compressed
                wasCompressed: false,
                processingTime: endTime - startTime
            )
            
        } catch let chatError as ChatError {
            throw chatError
        } catch {
            throw ChatError.fileProcessingFailed(.encodingFailed(reason: error.localizedDescription))
        }
    }
    
    // MARK: - Batch Processing
    
    /// Processes multiple files in batch
    /// - Parameters:
    ///   - files: Array of file data with metadata
    ///   - apiStyle: The target API style
    /// - Returns: Array of processing results
    /// - Throws: ChatError if any file fails to process
    static func processFiles(
        _ files: [(data: Data, fileName: String, mimeType: String)],
        for apiStyle: APIStyle
    ) throws -> [ProcessingResult] {
        var results: [ProcessingResult] = []
        
        for (data, fileName, mimeType) in files {
            let result = try processFile(
                data: data,
                fileName: fileName,
                mimeType: mimeType,
                for: apiStyle
            )
            results.append(result)
        }
        
        return results
    }
    
    // MARK: - Validation Helpers
    
    /// Pre-validates file before processing
    /// - Parameters:
    ///   - data: The file data
    ///   - mimeType: The MIME type
    ///   - apiStyle: The target API style
    /// - Returns: Validation result with recommendations
    static func validateFile(
        data: Data,
        mimeType: String,
        for apiStyle: APIStyle
    ) -> (isValid: Bool, errors: [FileProcessingError], recommendations: [String]) {
        var errors: [FileProcessingError] = []
        var recommendations: [String] = []
        
        // Format validation
        if let formatError = FileFormatValidator.validateFileFormat(mimeType, for: apiStyle) {
            errors.append(formatError)
        }
        
        // Size validation
        let sizeValidation = FileSizeValidator.validateSizeWithRecommendations(Int64(data.count), for: apiStyle)
        if let sizeError = sizeValidation.error {
            errors.append(sizeError)
        }
        
        if let recommendation = sizeValidation.recommendation {
            recommendations.append(recommendation)
        }
        
        // Image-specific validation
        let fileType = FileFormatValidator.fileType(from: mimeType)
        if fileType == .image && !ImageCompressor.isValidImageData(data) {
            errors.append(.invalidFileData(reason: "Invalid image data"))
        }
        
        return (isValid: errors.isEmpty, errors: errors, recommendations: recommendations)
    }
}
