import Foundation

/// Factory responsible for creating use case instances
final class UseCasesFactory {
    private let repositoriesFactory: RepositoriesFactory
    
    /// Initialize the factory with a repositories factory
    /// - Parameter repositoriesFactory: Factory that provides repository instances
    init(repositoriesFactory: RepositoriesFactory) {
        self.repositoriesFactory = repositoriesFactory
    }

    // MARK: - Observation Use Cases
    
    /// Creates an ObserveModelManagementChangesUseCase instance
    /// - Returns: An implementation of ObserveModelManagementChangesUseCaseProtocol
    func makeObserveModelManagementChangesUseCase() -> ObserveModelManagementChangesUseCaseProtocol {
        return ObserveModelManagementChangesUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    // MARK: - Provider Use Cases
    
    /// Creates a GetProviderUseCase instance
    /// - Returns: An implementation of GetProviderUseCase
    func makeGetProviderUseCase() -> GetProviderUseCase {
        return GetProviderUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a GetAllProvidersUseCase instance
    /// - Returns: An implementation of GetAllProvidersUseCase
    func makeGetAllProvidersUseCase() -> GetAllProvidersUseCase {
        return GetAllProvidersUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a CreateProviderUseCase instance
    /// - Returns: An implementation of CreateProviderUseCase
    func makeCreateProviderUseCase() -> CreateProviderUseCase {
        return CreateProviderUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates an UpdateProviderUseCase instance
    /// - Returns: An implementation of UpdateProviderUseCase
    func makeUpdateProviderUseCase() -> UpdateProviderUseCase {
        return UpdateProviderUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a DeleteProviderUseCase instance
    /// - Returns: An implementation of DeleteProviderUseCase
    func makeDeleteProviderUseCase() -> DeleteProviderUseCase {
        return DeleteProviderUseCase(
            repository: repositoriesFactory.makeLLMInstanceRepository(),
            keychainRepository: repositoriesFactory.makeKeychainRepository()
        )
    }
    
    /// Creates a GetProviderAPIKeyUseCase instance
    /// - Returns: An implementation of GetProviderAPIKeyUseCase
    func makeGetProviderAPIKeyUseCase() -> GetProviderAPIKeyUseCase {
        return GetProviderAPIKeyUseCase(
            repository: repositoriesFactory.makeLLMInstanceRepository(),
            keychainRepository: repositoriesFactory.makeKeychainRepository()
        )
    }
    
    /// Creates a SaveProviderAPIKeyUseCase instance
    /// - Returns: An implementation of SaveProviderAPIKeyUseCase
    func makeSaveProviderAPIKeyUseCase() -> SaveProviderAPIKeyUseCase {
        return SaveProviderAPIKeyUseCase(
            repository: repositoriesFactory.makeLLMInstanceRepository(),
            keychainRepository: repositoriesFactory.makeKeychainRepository()
        )
    }
    
    /// Creates a ClearProviderAPIKeyUseCase instance
    /// - Returns: An implementation of ClearProviderAPIKeyUseCase
    func makeClearProviderAPIKeyUseCase() -> ClearProviderAPIKeyUseCase {
        return ClearProviderAPIKeyUseCase(
            repository: repositoriesFactory.makeLLMInstanceRepository(),
            keychainRepository: repositoriesFactory.makeKeychainRepository()
        )
    }
    
    // MARK: - Model Use Cases
    
    /// Creates a GetModelUseCase instance
    /// - Returns: An implementation of GetModelUseCase
    func makeGetModelUseCase() -> GetModelUseCase {
        return GetModelUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }

    /// Creates a GetAllModelsUseCase instance
    /// - Returns: An implementation of GetAllModelsUseCase
    func makeGetAllModelsUseCase() -> GetAllModelsUseCase {
        return GetAllModelsUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a GetModelsForProviderUseCase instance
    /// - Returns: An implementation of GetModelsForProviderUseCase
    func makeGetModelsForProviderUseCase() -> GetModelsForProviderUseCase {
        return GetModelsForProviderUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a CreateModelUseCase instance
    /// - Returns: An implementation of CreateModelUseCase
    func makeCreateModelUseCase() -> CreateModelUseCase {
        return CreateModelUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates an UpdateModelUseCase instance
    /// - Returns: An implementation of UpdateModelUseCase
    func makeUpdateModelUseCase() -> UpdateModelUseCase {
        return UpdateModelUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a DeleteModelUseCase instance
    /// - Returns: An implementation of DeleteModelUseCase
    func makeDeleteModelUseCase() -> DeleteModelUseCase {
        return DeleteModelUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    // MARK: - Instance Use Cases
    
    /// Creates a GetInstanceUseCase instance
    /// - Returns: An implementation of GetInstanceUseCase
    func makeGetInstanceUseCase() -> GetInstanceUseCase {
        return GetInstanceUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a GetAllInstancesUseCase instance
    /// - Returns: An implementation of GetAllInstancesUseCase
    func makeGetAllInstancesUseCase() -> GetAllInstancesUseCase {
        return GetAllInstancesUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a GetInstancesForModelUseCase instance
    /// - Returns: An implementation of GetInstancesForModelUseCase
    func makeGetInstancesForModelUseCase() -> GetInstancesForModelUseCase {
        return GetInstancesForModelUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a CreateInstanceUseCase instance
    /// - Returns: An implementation of CreateInstanceUseCase
    func makeCreateInstanceUseCase() -> CreateInstanceUseCase {
        return CreateInstanceUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates an UpdateInstanceUseCase instance
    /// - Returns: An implementation of UpdateInstanceUseCase
    func makeUpdateInstanceUseCase() -> UpdateInstanceUseCase {
        return UpdateInstanceUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a DeleteInstanceUseCase instance
    /// - Returns: An implementation of DeleteInstanceUseCase
    func makeDeleteInstanceUseCase() -> DeleteInstanceUseCase {
        return DeleteInstanceUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a DuplicateInstanceUseCase instance
    /// - Returns: An implementation of DuplicateInstanceUseCase
    func makeDuplicateInstanceUseCase() -> DuplicateInstanceUseCase {
        return DuplicateInstanceUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a GetFavoritedInstancesUseCase instance
    /// - Returns: An implementation of GetFavoritedInstancesUseCase
    func makeGetFavoritedInstancesUseCase() -> GetFavoritedInstancesUseCase {
        return GetFavoritedInstancesUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a ToggleInstanceFavoriteUseCase instance
    /// - Returns: An implementation of ToggleInstanceFavoriteUseCase
    func makeToggleInstanceFavoriteUseCase() -> ToggleInstanceFavoriteUseCase {
        return ToggleInstanceFavoriteUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a GetInstancesWithRelatedEntitiesUseCase instance
    /// - Returns: An implementation of GetInstancesWithRelatedEntitiesUseCaseProtocol
    func makeGetInstancesWithRelatedEntitiesUseCase() -> GetInstancesWithRelatedEntitiesUseCaseProtocol {
        return GetInstancesWithRelatedEntitiesUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    // MARK: - Group Use Cases
    
    /// Creates a GetInstanceGroupUseCase instance
    /// - Returns: An implementation of GetInstanceGroupUseCase
    func makeGetInstanceGroupUseCase() -> GetInstanceGroupUseCase {
        return GetInstanceGroupUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a GetAllInstanceGroupsUseCase instance
    /// - Returns: An implementation of GetAllInstanceGroupsUseCase
    func makeGetAllInstanceGroupsUseCase() -> GetAllInstanceGroupsUseCase {
        return GetAllInstanceGroupsUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a CreateInstanceGroupUseCase instance
    /// - Returns: An implementation of CreateInstanceGroupUseCase
    func makeCreateInstanceGroupUseCase() -> CreateInstanceGroupUseCase {
        return CreateInstanceGroupUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates an UpdateInstanceGroupUseCase instance
    /// - Returns: An implementation of UpdateInstanceGroupUseCase
    func makeUpdateInstanceGroupUseCase() -> UpdateInstanceGroupUseCase {
        return UpdateInstanceGroupUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a DeleteInstanceGroupUseCase instance
    /// - Returns: An implementation of DeleteInstanceGroupUseCase
    func makeDeleteInstanceGroupUseCase() -> DeleteInstanceGroupUseCase {
        return DeleteInstanceGroupUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates an AddInstanceToGroupUseCase instance
    /// - Returns: An implementation of AddInstanceToGroupUseCase
    func makeAddInstanceToGroupUseCase() -> AddInstanceToGroupUseCase {
        return AddInstanceToGroupUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }
    
    /// Creates a RemoveInstanceFromGroupUseCase instance
    /// - Returns: An implementation of RemoveInstanceFromGroupUseCase
    func makeRemoveInstanceFromGroupUseCase() -> RemoveInstanceFromGroupUseCase {
        return RemoveInstanceFromGroupUseCase(repository: repositoriesFactory.makeLLMInstanceRepository())
    }

    // MARK: - Chat Session Use Cases
    
    /// Creates a CreateChatSessionUseCase instance
    /// - Returns: An implementation of CreateChatSessionUseCaseProtocol
    func makeCreateChatSessionUseCase() -> CreateChatSessionUseCaseProtocol {
        return CreateChatSessionUseCase(
            chatRepository: repositoriesFactory.makeChatRepository(),
            llmInstanceRepository: repositoriesFactory.makeLLMInstanceRepository(),
            userRepository: repositoriesFactory.makeUserSettingsRepository()
        )
    }
    
    /// Creates a GetChatSessionUseCase instance
    /// - Returns: An implementation of GetChatSessionUseCaseProtocol
    func makeGetChatSessionUseCase() -> GetChatSessionUseCaseProtocol {
        return GetChatSessionUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates a GetAllChatSessionsUseCase instance
    /// - Returns: An implementation of GetAllChatSessionsUseCaseProtocol
    func makeGetAllChatSessionsUseCase() -> GetAllChatSessionsUseCaseProtocol {
        return GetAllChatSessionsUseCase(
            chatRepository: repositoriesFactory.makeChatRepository(),
            appSettingsRepository: repositoriesFactory.makeAppSettingsRepository()
        )
    }
    
    /// Creates an UpdateChatSessionUseCase instance
    /// - Returns: An implementation of UpdateChatSessionUseCaseProtocol
    func makeUpdateChatSessionUseCase() -> UpdateChatSessionUseCaseProtocol {
        return UpdateChatSessionUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates a DeleteChatSessionUseCase instance
    /// - Returns: An implementation of DeleteChatSessionUseCaseProtocol
    func makeDeleteChatSessionUseCase() -> DeleteChatSessionUseCaseProtocol {
        return DeleteChatSessionUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates a DuplicateChatSessionUseCase instance
    /// - Returns: An implementation of DuplicateChatSessionUseCaseProtocol
    func makeDuplicateChatSessionUseCase() -> DuplicateChatSessionUseCaseProtocol {
        return DuplicateChatSessionUseCase(
            chatRepository: repositoriesFactory.makeChatRepository()
        )
    }
    
    // MARK: - Message Use Cases
    
    /// Creates a MessageTreeManagerUseCase instance (shared singleton)
    /// - Returns: An implementation of MessageTreeManagerUseCaseProtocol
    private lazy var sharedMessageTreeManager: MessageTreeManagerUseCaseProtocol = {
        return MessageTreeManagerUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }()
    
    /// Creates a MessageTreeManagerUseCase instance
    /// - Returns: An implementation of MessageTreeManagerUseCaseProtocol
    func makeMessageTreeManagerUseCase() -> MessageTreeManagerUseCaseProtocol {
        return sharedMessageTreeManager
    }
    
    /// Creates a PrepareMessagesUseCase instance
    /// - Returns: An implementation of PrepareMessagesUseCaseProtocol
    func makePrepareMessagesUseCase() -> PrepareMessagesUseCaseProtocol {
        return PrepareMessagesUseCase(
            chatRepository: repositoriesFactory.makeChatRepository(),
            messageTreeManagerUseCase: sharedMessageTreeManager
        )
    }
    
    /// Creates a SendMessageUseCase instance (shared singleton)
    /// - Returns: An implementation of SendMessageUseCaseProtocol
    private lazy var sharedSendMessageUseCase: SendMessageUseCaseProtocol = {
        return SendMessageUseCase(
            chatRepository: repositoriesFactory.makeChatRepository(),
            llmAPIService: repositoriesFactory.makeLLMAPIService(),
            messageTreeManager: sharedMessageTreeManager
        )
    }()

    /// Creates a SendMessageUseCase instance
    /// - Returns: An implementation of SendMessageUseCaseProtocol
    func makeSendMessageUseCase() -> SendMessageUseCaseProtocol {
        return sharedSendMessageUseCase
    }
    
    /// Creates a GetMessagesForSessionUseCase instance
    /// - Returns: An implementation of GetMessagesForSessionUseCaseProtocol
    func makeGetMessagesForSessionUseCase() -> GetMessagesForSessionUseCaseProtocol {
        return GetMessagesForSessionUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates a GetMessagesUseCase instance
    /// - Returns: An implementation of GetMessagesUseCaseProtocol
    func makeGetMessagesUseCase() -> GetMessagesUseCaseProtocol {
        return GetMessagesUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates a CreateMessageUseCase instance
    /// - Returns: An implementation of CreateMessageUseCaseProtocol
    func makeCreateMessageUseCase() -> CreateMessageUseCaseProtocol {
        return CreateMessageUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates an UpdateMessageUseCase instance
    /// - Returns: An implementation of UpdateMessageUseCaseProtocol
    func makeUpdateMessageUseCase() -> UpdateMessageUseCaseProtocol {
        return UpdateMessageUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates a DeleteMessageUseCase instance
    /// - Returns: An implementation of DeleteMessageUseCaseProtocol
    func makeDeleteMessageUseCase() -> DeleteMessageUseCaseProtocol {
        return DeleteMessageUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates a RegenerateSingleMessageUseCase instance
    /// - Returns: An implementation of RegenerateSingleMessageUseCaseProtocol
    func makeRegenerateSingleMessageUseCase() -> RegenerateSingleMessageUseCaseProtocol {
        return RegenerateSingleMessageUseCase(
            prepareMessagesUseCase: makePrepareMessagesUseCase(),
            sendMessageUseCase: makeSendMessageUseCase()
        )
    }

    /// Creates a RegenerateSingleMessageWithPromptUseCase instance
    /// - Returns: An implementation of RegenerateSingleMessageWithPromptUseCaseProtocol
    func makeRegenerateSingleMessageWithPromptUseCase() -> RegenerateSingleMessageWithPromptUseCaseProtocol {
        return RegenerateSingleMessageWithPromptUseCase(
            prepareMessagesUseCase: makePrepareMessagesUseCase(),
            sendMessageUseCase: makeSendMessageUseCase()
        )
    }

    /// Creates a RewritePromptWithPromptUseCase instance
    /// - Returns: An implementation of RewritePromptWithPromptUseCaseProtocol
    func makeRewritePromptWithPromptUseCase() -> RewritePromptWithPromptUseCaseProtocol {
        return RewritePromptWithPromptUseCase(
            prepareMessagesUseCase: makePrepareMessagesUseCase(),
            sendMessageUseCase: makeSendMessageUseCase()
        )
    }

    /// Creates a GenerateChatTitleUseCase instance
    /// - Returns: An implementation of GenerateChatTitleUseCaseProtocol
    func makeGenerateChatTitleUseCase() -> GenerateChatTitleUseCaseProtocol {
        return GenerateChatTitleUseCase(
            prepareMessagesUseCase: makePrepareMessagesUseCase(),
            sendMessageUseCase: makeSendMessageUseCase()
        )
    }
    
    /// Creates a RegenerateAllMessagesUseCase instance
    /// - Returns: An implementation of RegenerateAllMessagesUseCaseProtocol
    func makeRegenerateAllMessagesUseCase() -> RegenerateAllMessagesUseCaseProtocol {
        return RegenerateAllMessagesUseCase(
            prepareMessagesUseCase: makePrepareMessagesUseCase(),
            sendMessageUseCase: makeSendMessageUseCase()
        )
    }
    
    /// Creates an EditMessageUseCase instance
    /// - Returns: An implementation of EditMessageUseCaseProtocol
    func makeEditMessageUseCase() -> EditMessageUseCaseProtocol {
        return EditMessageUseCase(
            chatRepository: repositoriesFactory.makeChatRepository(),
            prepareMessagesUseCase: makePrepareMessagesUseCase(),
            sendMessageUseCase: makeSendMessageUseCase()
        )
    }
    
    // MARK: - Chat Session Setting Use Cases
    
    /// Creates a GetChatSessionSettingUseCase instance
    /// - Returns: An implementation of GetChatSessionSettingUseCaseProtocol
    func makeGetChatSessionSettingUseCase() -> GetChatSessionSettingUseCaseProtocol {
        return GetChatSessionSettingUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates a GetDefaultChatSessionSettingUseCase instance
    /// - Returns: An implementation of GetDefaultChatSessionSettingUseCaseProtocol
    func makeGetDefaultChatSessionSettingUseCase() -> GetDefaultChatSessionSettingUseCaseProtocol {
        return GetDefaultChatSessionSettingUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }

    /// Creates a GetAllChatSessionSettingsUseCase instance
    /// - Returns: An implementation of GetAllChatSessionSettingsUseCaseProtocol
    func makeGetAllChatSessionSettingsUseCase() -> GetAllChatSessionSettingsUseCaseProtocol {
        return GetAllChatSessionSettingsUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates a CreateChatSessionSettingUseCase instance
    /// - Returns: An implementation of CreateChatSessionSettingUseCaseProtocol
    func makeCreateChatSessionSettingUseCase() -> CreateChatSessionSettingUseCaseProtocol {
        return CreateChatSessionSettingUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }
    
    /// Creates an UpdateChatSessionSettingUseCase instance
    /// - Returns: An implementation of UpdateChatSessionSettingUseCaseProtocol
    func makeUpdateChatSessionSettingUseCase() -> UpdateChatSessionSettingUseCaseProtocol {
        return UpdateChatSessionSettingUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }

    /// Creates a DeleteChatSessionSettingUseCase instance
    /// - Returns: An implementation of DeleteChatSessionSettingUseCaseProtocol
    func makeDeleteChatSessionSettingUseCase() -> DeleteChatSessionSettingUseCaseProtocol {
        return DeleteChatSessionSettingUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }

    /// Creates an UpdateUserDefaultChatSettingUseCase instance
    /// - Returns: An implementation of UpdateUserDefaultChatSettingUseCaseProtocol
    func makeUpdateUserDefaultChatSettingUseCase() -> UpdateUserDefaultChatSettingUseCaseProtocol {
        return UpdateUserDefaultChatSettingUseCase(
            getCurrentUserUseCase: makeGetCurrentUserUseCase(),
            updateUserUseCase: makeUpdateUserUseCase()
        )
    }
    
    // MARK: - Chat Observation Use Cases
    
    /// Creates an ObserveChatsChangesUseCase instance
    /// - Returns: An implementation of ObserveChatsChangesUseCaseProtocol
    func makeObserveChatsChangesUseCase() -> ObserveChatsChangesUseCaseProtocol {
        return ObserveChatsChangesUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }

    // MARK: - Message Action Use Cases

    /// Creates a CreateMessageActionUseCase instance
    /// - Returns: An implementation of CreateMessageActionUseCaseProtocol
    func makeCreateMessageActionUseCase() -> CreateMessageActionUseCaseProtocol {
        return CreateMessageActionUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }

    /// Creates a GetMessageActionUseCase instance
    /// - Returns: An implementation of GetMessageActionUseCaseProtocol
    func makeGetMessageActionUseCase() -> GetMessageActionUseCaseProtocol {
        return GetMessageActionUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }

    /// Creates a GetAllMessageActionsUseCase instance
    /// - Returns: An implementation of GetAllMessageActionsUseCaseProtocol
    func makeGetAllMessageActionsUseCase() -> GetAllMessageActionsUseCaseProtocol {
        return GetAllMessageActionsUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }

    /// Creates an UpdateMessageActionUseCase instance
    /// - Returns: An implementation of UpdateMessageActionUseCaseProtocol
    func makeUpdateMessageActionUseCase() -> UpdateMessageActionUseCaseProtocol {
        return UpdateMessageActionUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }

    /// Creates a DeleteMessageActionUseCase instance
    /// - Returns: An implementation of DeleteMessageActionUseCaseProtocol
    func makeDeleteMessageActionUseCase() -> DeleteMessageActionUseCaseProtocol {
        return DeleteMessageActionUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }

    /// Creates a GetMessageActionUsageUseCase instance
    /// - Returns: An implementation of GetMessageActionUsageUseCaseProtocol
    func makeGetMessageActionUsageUseCase() -> GetMessageActionUsageUseCaseProtocol {
        return GetMessageActionUsageUseCase(chatRepository: repositoriesFactory.makeChatRepository())
    }

    // MARK: - User Use Cases

    /// Creates a CreateUserUseCase instance
    /// - Returns: An implementation of CreateUserUseCaseProtocol
    func makeCreateUserUseCase() -> CreateUserUseCaseProtocol {
        return CreateUserUseCase(userRepository: repositoriesFactory.makeUserSettingsRepository())
    }

    /// Creates a GetUserUseCase instance
    /// - Returns: An implementation of GetUserUseCaseProtocol
    func makeGetUserUseCase() -> GetUserUseCaseProtocol {
        return GetUserUseCase(userRepository: repositoriesFactory.makeUserSettingsRepository())
    }

    /// Creates a GetAllUsersUseCase instance
    /// - Returns: An implementation of GetAllUsersUseCaseProtocol
    func makeGetAllUsersUseCase() -> GetAllUsersUseCaseProtocol {
        return GetAllUsersUseCase(userRepository: repositoriesFactory.makeUserSettingsRepository())
    }

    /// Creates an UpdateUserUseCase instance
    /// - Returns: An implementation of UpdateUserUseCaseProtocol
    func makeUpdateUserUseCase() -> UpdateUserUseCaseProtocol {
        return UpdateUserUseCase(userRepository: repositoriesFactory.makeUserSettingsRepository())
    }

    /// Creates a DeleteUserUseCase instance
    /// - Returns: An implementation of DeleteUserUseCaseProtocol
    func makeDeleteUserUseCase() -> DeleteUserUseCaseProtocol {
        return DeleteUserUseCase(userRepository: repositoriesFactory.makeUserSettingsRepository())
    }

    /// Creates a GetCurrentUserUseCase instance
    /// - Returns: An implementation of GetCurrentUserUseCaseProtocol
    func makeGetCurrentUserUseCase() -> GetCurrentUserUseCaseProtocol {
        return GetCurrentUserUseCase(userRepository: repositoriesFactory.makeUserSettingsRepository())
    }

    /// Creates a SetCurrentUserUseCase instance
    /// - Returns: An implementation of SetCurrentUserUseCaseProtocol
    func makeSetCurrentUserUseCase() -> SetCurrentUserUseCaseProtocol {
        return SetCurrentUserUseCase(userRepository: repositoriesFactory.makeUserSettingsRepository())
    }
}
