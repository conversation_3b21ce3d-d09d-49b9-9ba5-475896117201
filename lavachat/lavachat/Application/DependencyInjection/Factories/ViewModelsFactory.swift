import Foundation

/// Factory responsible for creating ViewModel instances
final class ViewModelsFactory {
    private let useCasesFactory: UseCasesFactory
    
    /// Initialize the factory with a use cases factory
    /// - Parameter useCasesFactory: Factory that provides use case instances
    init(useCasesFactory: UseCasesFactory) {
        self.useCasesFactory = useCasesFactory
    }

    // MARK: - Tab ViewModels

    /// Creates a ModelsViewModel
    /// - Returns: A configured ModelsViewModel
    func makeModelsViewModel() -> ModelsViewModel {
        return ModelsViewModel(
            getAllProvidersUseCase: useCasesFactory.makeGetAllProvidersUseCase(),
            getAllModelsUseCase: useCasesFactory.makeGetAllModelsUseCase(),
            getAllInstancesUseCase: useCasesFactory.makeGetAllInstancesUseCase(),
            getFavoritedInstancesUseCase: useCasesFactory.makeGetFavoritedInstancesUseCase(),
            getAllInstanceGroupsUseCase: useCasesFactory.makeGetAllInstanceGroupsUseCase(),
            getModelUseCase: useCasesFactory.makeGetModelUseCase(),
            observeModelManagementChangesUseCase: useCasesFactory.makeObserveModelManagementChangesUseCase()
        )
    }
    
    /// Creates a ChatsViewModel
    /// - Parameters:
    ///   - container: DIContainer reference for creating ChatViews
    /// - Returns: A configured ChatsViewModel
    func makeChatsViewModel(container: DIContainer) -> ChatsViewModel {
        return ChatsViewModel(
            getAllSessionsUseCase: useCasesFactory.makeGetAllChatSessionsUseCase(),
            deleteSessionUseCase: useCasesFactory.makeDeleteChatSessionUseCase(),
            getMessagesUseCase: useCasesFactory.makeGetMessagesUseCase(),
            observeChatsChangesUseCase: useCasesFactory.makeObserveChatsChangesUseCase(),
            getInstancesWithRelatedEntitiesUseCase: useCasesFactory.makeGetInstancesWithRelatedEntitiesUseCase(),
            container: container
        )
    }
    
    /// Creates a ChatViewModel
    /// - Parameters:
    ///   - chatSessionId: ID of the chat session to load
    ///   - onDuplicateChatSession: Optional callback for when a chat is duplicated
    /// - Returns: A configured ChatViewModel
    func makeChatViewModel(chatSessionId: UUID, onDuplicateChatSession: ChatDuplicationAction? = nil) -> ChatViewModel {
        print("🔄 makeChatViewModel: \(chatSessionId)")
        return ChatViewModel(
            chatSessionId: chatSessionId,
            getChatSessionUseCase: useCasesFactory.makeGetChatSessionUseCase(),
            prepareMessagesUseCase: useCasesFactory.makePrepareMessagesUseCase(),
            sendMessageUseCase: useCasesFactory.makeSendMessageUseCase(),
            messageTreeManagerUseCase: useCasesFactory.makeMessageTreeManagerUseCase(),
            observeChatsChangesUseCase: useCasesFactory.makeObserveChatsChangesUseCase(),
            getChatSessionSettingUseCase: useCasesFactory.makeGetChatSessionSettingUseCase(),
            getInstancesWithRelatedEntitiesUseCase: useCasesFactory.makeGetInstancesWithRelatedEntitiesUseCase(),
            regenerateSingleMessageUseCase: useCasesFactory.makeRegenerateSingleMessageUseCase(),
            regenerateSingleMessageWithPromptUseCase: useCasesFactory.makeRegenerateSingleMessageWithPromptUseCase(),
            regenerateAllMessagesUseCase: useCasesFactory.makeRegenerateAllMessagesUseCase(),
            rewritePromptWithPromptUseCase: useCasesFactory.makeRewritePromptWithPromptUseCase(),
            generateChatTitleUseCase: useCasesFactory.makeGenerateChatTitleUseCase(),
            duplicateChatSessionUseCase: useCasesFactory.makeDuplicateChatSessionUseCase(),
            onDuplicateChatSession: onDuplicateChatSession
        )
    }

    /// Creates a ChatSessionDetailViewModel
    /// - Parameter chatSessionId: ID of the chat session to manage
    /// - Returns: A configured ChatSessionDetailViewModel
    func makeChatSessionDetailViewModel(chatSessionId: UUID) -> ChatSessionDetailViewModel {
        return ChatSessionDetailViewModel(
            chatSessionId: chatSessionId,
            getChatSessionUseCase: useCasesFactory.makeGetChatSessionUseCase(),
            updateChatSessionUseCase: useCasesFactory.makeUpdateChatSessionUseCase(),
            getChatSessionSettingUseCase: useCasesFactory.makeGetChatSessionSettingUseCase(),
            getAllChatSessionSettingsUseCase: useCasesFactory.makeGetAllChatSessionSettingsUseCase(),
            updateChatSessionSettingUseCase: useCasesFactory.makeUpdateChatSessionSettingUseCase(),
            getInstanceUseCase: useCasesFactory.makeGetInstanceUseCase()
        )
    }

    // MARK: - Provider ViewModels
    
    /// Creates a ProviderDetailViewModel
    /// - Parameter providerId: Optional UUID of the provider to display/edit, nil for creation mode
    /// - Returns: A configured ProviderDetailViewModel
    func makeProviderDetailViewModel(providerId: UUID?) -> ProviderDetailViewModel {
        return ProviderDetailViewModel(
            providerId: providerId,
            getProviderUseCase: useCasesFactory.makeGetProviderUseCase(),
            getModelsForProviderUseCase: useCasesFactory.makeGetModelsForProviderUseCase(),
            createProviderUseCase: useCasesFactory.makeCreateProviderUseCase(),
            updateProviderUseCase: useCasesFactory.makeUpdateProviderUseCase(),
            deleteProviderUseCase: useCasesFactory.makeDeleteProviderUseCase(),
            getProviderAPIKeyUseCase: useCasesFactory.makeGetProviderAPIKeyUseCase(),
            saveProviderAPIKeyUseCase: useCasesFactory.makeSaveProviderAPIKeyUseCase(),
            clearProviderAPIKeyUseCase: useCasesFactory.makeClearProviderAPIKeyUseCase()
        )
    }
    
    /// Creates a ProviderListViewModel
    /// - Returns: A configured ProviderListViewModel
    func makeProviderListViewModel() -> Any {
        // Placeholder for future implementation
        fatalError("ProviderListViewModel not yet implemented")
    }
    
    // MARK: - Model ViewModels
    
    /// Creates a ModelDetailViewModel
    /// - Parameters:
    ///   - modelId: Optional UUID of the model to display/edit, nil for creation mode
    ///   - providerId: Optional UUID of the provider when creating a new model, nil for existing model
    /// - Returns: A configured ModelDetailViewModel
    func makeModelDetailViewModel(modelId: UUID?, providerId: UUID?) -> ModelDetailViewModel {
        return ModelDetailViewModel(
            modelId: modelId,
            providerId: providerId,
            getModelUseCase: useCasesFactory.makeGetModelUseCase(),
            getProviderUseCase: useCasesFactory.makeGetProviderUseCase(),
            getInstancesForModelUseCase: useCasesFactory.makeGetInstancesForModelUseCase(),
            createModelUseCase: useCasesFactory.makeCreateModelUseCase(),
            updateModelUseCase: useCasesFactory.makeUpdateModelUseCase(),
            deleteModelUseCase: useCasesFactory.makeDeleteModelUseCase(),
            createInstanceUseCase: useCasesFactory.makeCreateInstanceUseCase()
        )
    }
    
    /// Creates a ModelListViewModel
    /// - Parameter providerId: UUID of the provider to list models for
    /// - Returns: A configured ModelListViewModel
    func makeModelListViewModel(providerId: UUID) -> Any {
        // Placeholder for future implementation
        fatalError("ModelListViewModel not yet implemented")
    }
    
    // MARK: - Instance ViewModels
    
    /// Creates an InstanceDetailViewModel
    /// - Parameters:
    ///   - instanceId: Optional UUID of the instance to display/edit, nil for creation mode
    ///   - modelId: UUID of the model when creating a new instance
    /// - Returns: A configured InstanceDetailViewModel
    func makeInstanceDetailViewModel(instanceId: UUID?, modelId: UUID?) -> InstanceDetailViewModel {
        return InstanceDetailViewModel(
            instanceId: instanceId,
            modelIdForNewInstance: modelId,
            getInstanceUseCase: useCasesFactory.makeGetInstanceUseCase(),
            getModelUseCase: useCasesFactory.makeGetModelUseCase(),
            getProviderUseCase: useCasesFactory.makeGetProviderUseCase(),
            createInstanceUseCase: useCasesFactory.makeCreateInstanceUseCase(),
            updateInstanceUseCase: useCasesFactory.makeUpdateInstanceUseCase(),
            deleteInstanceUseCase: useCasesFactory.makeDeleteInstanceUseCase(),
            duplicateInstanceUseCase: useCasesFactory.makeDuplicateInstanceUseCase(),
            toggleInstanceFavoriteUseCase: useCasesFactory.makeToggleInstanceFavoriteUseCase(),
            getModelsForProviderUseCase: useCasesFactory.makeGetModelsForProviderUseCase(),
            getAllProvidersUseCase: useCasesFactory.makeGetAllProvidersUseCase()
        )
    }
    
    /// Creates an InstanceListViewModel
    /// - Returns: A configured InstanceListViewModel
    func makeInstanceListViewModel() -> Any {
        // Placeholder for future implementation
        fatalError("InstanceListViewModel not yet implemented")
    }
    
    // MARK: - Group ViewModels
    
    /// Creates an InstanceGroupViewModel
    /// - Parameter groupId: Optional UUID of the group to display/edit, nil for creation mode
    /// - Returns: A configured InstanceGroupViewModel
    func makeInstanceGroupViewModel(groupId: UUID?) -> Any {
        // Placeholder for future implementation
        fatalError("InstanceGroupViewModel not yet implemented")
    }
    
    /// Creates an InstanceGroupListViewModel
    /// - Returns: A configured InstanceGroupListViewModel
    func makeInstanceGroupListViewModel() -> Any {
        // Placeholder for future implementation
        fatalError("InstanceGroupListViewModel not yet implemented")
    }
    
    // MARK: - Chat ViewModels
    
    /// Creates a NewChatSheetViewModel for chat creation
    /// - Parameter onCreateNewChat: Callback for when a new chat is created with a selected instance
    /// - Returns: A configured NewChatSheetViewModel
    func makeNewChatSheetViewModel(onCreateNewChat: @escaping NewChatCreationAction) -> NewChatSheetViewModel {
        return NewChatSheetViewModel(
            getAllProvidersUseCase: useCasesFactory.makeGetAllProvidersUseCase(),
            getAllModelsUseCase: useCasesFactory.makeGetAllModelsUseCase(),
            getAllInstancesUseCase: useCasesFactory.makeGetAllInstancesUseCase(),
            getModelUseCase: useCasesFactory.makeGetModelUseCase(),
            observeModelManagementChangesUseCase: useCasesFactory.makeObserveModelManagementChangesUseCase(),
            createChatSessionUseCase: useCasesFactory.makeCreateChatSessionUseCase(),
            onCreateNewChat: onCreateNewChat
        )
    }
    
    /// Creates a NewChatSheetViewModel for instance selection
    /// - Parameters:
    ///   - mode: The sheet mode (chatCreation or instanceSelection)
    ///   - onSelectInstance: Callback for instance selection
    /// - Returns: A configured NewChatSheetViewModel
    func makeNewChatSheetViewModel(
        mode: SheetMode,
        onSelectInstance: @escaping InstanceSelectionAction
    ) -> NewChatSheetViewModel {
        return NewChatSheetViewModel(
            mode: mode,
            getAllProvidersUseCase: useCasesFactory.makeGetAllProvidersUseCase(),
            getAllModelsUseCase: useCasesFactory.makeGetAllModelsUseCase(),
            getAllInstancesUseCase: useCasesFactory.makeGetAllInstancesUseCase(),
            getModelUseCase: useCasesFactory.makeGetModelUseCase(),
            observeModelManagementChangesUseCase: useCasesFactory.makeObserveModelManagementChangesUseCase(),
            createChatSessionUseCase: useCasesFactory.makeCreateChatSessionUseCase(),
            onSelectInstance: onSelectInstance
        )
    }

    /// Creates a NewChatSheetViewModel for auxiliary instance selection
    /// - Parameters:
    ///   - mode: The sheet mode (auxiliaryInstanceSelection)
    ///   - onSelectAuxiliaryInstance: Callback for auxiliary instance selection
    /// - Returns: A configured NewChatSheetViewModel
    func makeNewChatSheetViewModel(
        mode: SheetMode,
        onSelectAuxiliaryInstance: @escaping AuxiliaryInstanceSelectionAction
    ) -> NewChatSheetViewModel {
        return NewChatSheetViewModel(
            mode: mode,
            getAllProvidersUseCase: useCasesFactory.makeGetAllProvidersUseCase(),
            getAllModelsUseCase: useCasesFactory.makeGetAllModelsUseCase(),
            getAllInstancesUseCase: useCasesFactory.makeGetAllInstancesUseCase(),
            getModelUseCase: useCasesFactory.makeGetModelUseCase(),
            observeModelManagementChangesUseCase: useCasesFactory.makeObserveModelManagementChangesUseCase(),
            createChatSessionUseCase: useCasesFactory.makeCreateChatSessionUseCase(),
            onSelectAuxiliaryInstance: onSelectAuxiliaryInstance
        )
    }

    // MARK: - Model Management ViewModels

    /// Creates a NewModelsSheetViewModel
    /// - Returns: A configured NewModelsSheetViewModel
    func makeNewModelsSheetViewModel() -> NewModelsSheetViewModel {
        return NewModelsSheetViewModel(
            getAllProvidersUseCase: useCasesFactory.makeGetAllProvidersUseCase(),
            getAllModelsUseCase: useCasesFactory.makeGetAllModelsUseCase(),
            observeModelManagementChangesUseCase: useCasesFactory.makeObserveModelManagementChangesUseCase()
        )
    }

    // MARK: - Settings ViewModels

    /// Creates a SettingsViewModel
    /// - Returns: A configured SettingsViewModel
    func makeSettingsViewModel() -> SettingsViewModel {
        return SettingsViewModel()
    }

    /// Creates a GeneralSettingsViewModel
    /// - Returns: A configured GeneralSettingsViewModel
    func makeGeneralSettingsViewModel() -> GeneralSettingsViewModel {
        return GeneralSettingsViewModel(
            getCurrentUserUseCase: useCasesFactory.makeGetCurrentUserUseCase(),
            updateUserUseCase: useCasesFactory.makeUpdateUserUseCase()
        )
    }

    /// Creates a MyActionsViewModel
    /// - Returns: A configured MyActionsViewModel
    func makeMyActionsViewModel() -> MyActionsViewModel {
        return MyActionsViewModel(
            getAllMessageActionsUseCase: useCasesFactory.makeGetAllMessageActionsUseCase()
        )
    }

    /// Creates an ActionManagementViewModel
    /// - Parameter category: The MessageActionCategory to manage
    /// - Returns: A configured ActionManagementViewModel
    func makeActionManagementViewModel(category: MessageActionCategory) -> ActionManagementViewModel {
        return ActionManagementViewModel(
            category: category,
            getAllMessageActionsUseCase: useCasesFactory.makeGetAllMessageActionsUseCase()
        )
    }

    /// Creates an ActionEditorViewModel
    /// - Parameters:
    ///   - category: The MessageActionCategory for the action
    ///   - editingAction: Optional action to edit, nil for creating new action
    /// - Returns: A configured ActionEditorViewModel
    func makeActionEditorViewModel(category: MessageActionCategory, editingAction: MessageAction?) -> ActionEditorViewModel {
        return ActionEditorViewModel(
            category: category,
            editingAction: editingAction,
            createMessageActionUseCase: useCasesFactory.makeCreateMessageActionUseCase(),
            updateMessageActionUseCase: useCasesFactory.makeUpdateMessageActionUseCase(),
            deleteMessageActionUseCase: useCasesFactory.makeDeleteMessageActionUseCase(),
            getMessageActionUsageUseCase: useCasesFactory.makeGetMessageActionUsageUseCase()
        )
    }

    /// Creates a MyChatSettingsViewModel
    /// - Returns: A configured MyChatSettingsViewModel
    func makeMyChatSettingsViewModel() -> MyChatSettingsViewModel {
        return MyChatSettingsViewModel(
            getAllChatSessionSettingsUseCase: useCasesFactory.makeGetAllChatSessionSettingsUseCase(),
            getCurrentUserUseCase: useCasesFactory.makeGetCurrentUserUseCase(),
            updateUserDefaultChatSettingUseCase: useCasesFactory.makeUpdateUserDefaultChatSettingUseCase(),
            getDefaultChatSessionSettingUseCase: useCasesFactory.makeGetDefaultChatSessionSettingUseCase()
        )
    }

    /// Creates a ChatSettingEditorViewModel
    /// - Parameters:
    ///   - editingSetting: Optional setting to edit, nil for creating new setting
    /// - Returns: A configured ChatSettingEditorViewModel
    func makeChatSettingEditorViewModel(editingSetting: ChatSessionSetting?) -> ChatSettingEditorViewModel {
        return ChatSettingEditorViewModel(
            editingSetting: editingSetting,
            createChatSessionSettingUseCase: useCasesFactory.makeCreateChatSessionSettingUseCase(),
            updateChatSessionSettingUseCase: useCasesFactory.makeUpdateChatSessionSettingUseCase(),
            deleteChatSessionSettingUseCase: useCasesFactory.makeDeleteChatSessionSettingUseCase(),
            getChatSessionSettingUseCase: useCasesFactory.makeGetChatSessionSettingUseCase(),
            getAllMessageActionsUseCase: useCasesFactory.makeGetAllMessageActionsUseCase(),
            getInstanceUseCase: useCasesFactory.makeGetInstanceUseCase(),
            getCurrentUserUseCase: useCasesFactory.makeGetCurrentUserUseCase()
        )
    }
}
