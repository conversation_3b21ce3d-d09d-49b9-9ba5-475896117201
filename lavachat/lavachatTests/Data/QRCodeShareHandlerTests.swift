import XCTest
import UIKit
@testable import lavachat

final class QRCodeShareHandlerTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var qrCodeHandler: QRCodeShareHandler!
    var testUserId: UUID!
    
    // Test data
    var testAction: MessageAction!
    var testSetting: ChatSessionSetting!
    var testProvider: LLMProvider!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        qrCodeHandler = QRCodeShareHandler()
        testUserId = UUID()
        setupTestData()
    }
    
    override func tearDown() {
        super.tearDown()
        qrCodeHandler = nil
        testUserId = nil
    }
    
    private func setupTestData() {
        testAction = MessageAction(
            name: "Test Action",
            icon: "star.fill",
            actionType: .assistantRegenerate,
            prompts: ["Test prompt"],
            targetLLMInstanceId: nil,
            metadata: ["category": "test"]
        )

        let messageActionSettings = MessageActionSettings(
            actionPanelActions: [],
            userMessageActions: [],
            assistantMessageCardActions: [testAction.id],
            assistantMessageMenuActions: []
        )

        testSetting = ChatSessionSetting(
            name: "Test Setting",
            isSystemDefault: false,
            shouldExpandThinking: true,
            messageActionSettings: messageActionSettings,
            shouldAutoGenerateTitle: true,
            contextMessageCount: 10
        )

        testProvider = LLMProvider(
            name: "Test Provider",
            apiBaseUrl: "https://api.test.com",
            providerType: .userApiKey,
            apiKeyStored: false, // Cleaned for sharing
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: true,
            isUserModified: false
        )
    }
    
    // MARK: - QR Code Generation Tests
    
    func testGenerateQRCodeForMessageAction() async throws {
        // Given - Use a mock iCloud URL instead of raw JSON data
        let mockiCloudURL = "https://www.icloud.com/share/test-message-action-share"

        // When
        let qrCodeImageData = try await qrCodeHandler.generateQRCode(
            for: mockiCloudURL,
            size: CGSize(width: 300, height: 300)
        )

        // Then
        XCTAssertNotNil(qrCodeImageData)
        XCTAssertGreaterThan(qrCodeImageData.count, 0)

        // Verify we can create a UIImage from the data
        let image = UIImage(data: qrCodeImageData)
        XCTAssertNotNil(image)
        XCTAssertEqual(image?.size.width, image?.size.height) // QR codes are square
    }
    
    func testGenerateQRCodeForChatSessionSetting() async throws {
        // Given - Use a mock iCloud URL instead of raw JSON data
        let mockiCloudURL = "https://www.icloud.com/share/test-chat-session-setting-share"

        // When
        let qrCodeImageData = try await qrCodeHandler.generateQRCode(
            for: mockiCloudURL,
            size: CGSize(width: 300, height: 300)
        )

        // Then
        XCTAssertNotNil(qrCodeImageData)
        XCTAssertGreaterThan(qrCodeImageData.count, 0)
    }
    
    func testGenerateQRCodeForLargeData() async throws {
        // Given - Create a very long URL to test QR code size limits
        let veryLongURL = "https://www.icloud.com/share/test-very-long-url-that-might-exceed-qr-code-capacity-limits-and-should-either-succeed-or-fail-gracefully-with-proper-error-handling-for-data-size-constraints-in-qr-code-generation-process-which-has-specific-limits-based-on-error-correction-level-and-encoding-format-used-by-the-underlying-core-image-framework-implementation"

        // When & Then
        do {
            let qrCodeImageData = try await qrCodeHandler.generateQRCode(
                for: veryLongURL,
                size: CGSize(width: 300, height: 300)
            )
            // If it succeeds, verify the image
            XCTAssertNotNil(qrCodeImageData)
            XCTAssertGreaterThan(qrCodeImageData.count, 0)
        } catch {
            // If it fails, it should be due to data size limits
            XCTAssertTrue(error is ShareError)
            if case .qrCodeGenerationFailed = error as? ShareError {
                // This is expected for very large data
                XCTAssertTrue(true)
            } else {
                XCTFail("Unexpected error type: \(error)")
            }
        }
    }
    
    // MARK: - QR Code Scanning Tests
    
    func testScanQRCodeRoundTrip() async throws {
        // Given - Generate a QR code with a mock iCloud URL
        let originalURL = "https://www.icloud.com/share/test-message-action-round-trip"

        let qrCodeImageData = try await qrCodeHandler.generateQRCode(
            for: originalURL,
            size: CGSize(width: 300, height: 300)
        )

        // When - Scan the generated QR code
        let scannedStrings = try await qrCodeHandler.scanQRCode(from: qrCodeImageData)

        // Then - Verify we got back the original URL string
        XCTAssertEqual(scannedStrings.count, 1)
        let scannedURL = scannedStrings.first!
        XCTAssertEqual(scannedURL, originalURL)
    }
    
    func testScanInvalidQRCode() async throws {
        // Given - Create an image that's not a valid QR code
        let invalidImage = UIImage(systemName: "star.fill")!

        // When & Then
        do {
            _ = try await qrCodeHandler.scanQRCode(from: invalidImage)
            XCTFail("Should throw error for invalid QR code")
        } catch {
            XCTAssertTrue(error is ShareError)
        }
    }
    
    // MARK: - QR Code Quality Tests
    
    // MARK: - QR Code Quality Tests
    
    func testQRCodeQualityLevels() async throws {
        // Given - Use a mock iCloud URL
        let testURL = "https://www.icloud.com/share/test-quality-levels"

        // Test QR code generation (the actual implementation uses high error correction by default)
        // When
        let qrCodeImageData = try await qrCodeHandler.generateQRCode(
            for: testURL,
            size: CGSize(width: 300, height: 300)
        )

        // Then
        XCTAssertNotNil(qrCodeImageData)
        XCTAssertGreaterThan(qrCodeImageData.count, 0)

        // Verify we can scan it back
        let scannedStrings = try await qrCodeHandler.scanQRCode(from: qrCodeImageData)
        XCTAssertEqual(scannedStrings.count, 1)
        XCTAssertEqual(scannedStrings.first!, testURL)
    }
    
    // MARK: - Performance Tests
    
    func testQRCodeGenerationPerformance() {
        // Given - Use a mock iCloud URL
        let testURL = "https://www.icloud.com/share/test-performance-measurement"

        // When & Then
        measure {
            Task {
                do {
                    _ = try await qrCodeHandler.generateQRCode(
                        for: testURL,
                        size: CGSize(width: 300, height: 300)
                    )
                } catch {
                    XCTFail("QR code generation failed: \(error)")
                }
            }
        }
    }
    
    func testQRCodeScanningPerformance() async throws {
        // Given - Generate a QR code with a mock iCloud URL
        let testURL = "https://www.icloud.com/share/test-scanning-performance"

        let qrCodeImageData = try await qrCodeHandler.generateQRCode(
            for: testURL,
            size: CGSize(width: 300, height: 300)
        )

        // When & Then
        measure {
            Task {
                do {
                    _ = try await qrCodeHandler.scanQRCode(from: qrCodeImageData)
                } catch {
                    XCTFail("QR code scanning failed: \(error)")
                }
            }
        }
    }
    
    // MARK: - Edge Cases Tests
    
    func testEmptyDataQRCode() async {
        // Given - Empty string data for QR code generation
        let emptyString = ""
        let size = CGSize(width: 200, height: 200)

        // When & Then
        do {
            _ = try await qrCodeHandler.generateQRCode(for: emptyString, size: size)
            XCTFail("Should throw error for empty data")
        } catch {
            XCTAssertTrue(error is ShareError)
        }
    }
}
